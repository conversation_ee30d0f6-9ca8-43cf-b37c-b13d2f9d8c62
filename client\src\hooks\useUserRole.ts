import { useQuery } from "@tanstack/react-query";
import { getQueryFn } from "@/lib/queryClient";
import { useAuth } from "@/hooks/useAuth";

export type UserRoleType = 'SuperUser' | 'general_contractor' | 'contractor' | 'loading';

export function useUserRole() {
  const { user, isAuthenticated } = useAuth();
  
  // Fetch user profile to get actual role
  const { data: userProfile, isLoading: isUserLoading } = useQuery({
    queryKey: ["/api/auth/user"],
    enabled: isAuthenticated,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Fetch contractor profile to determine contractor-specific role
  const { data: contractorProfile, isLoading: isProfileLoading } = useQuery({
    queryKey: ["/api/contractors/profile"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: isAuthenticated,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Determine user role based on user profile and contractor classification
  const getUserRole = (): UserRoleType => {
    if (isProfileLoading || isUserLoading || !isAuthenticated) {
      return 'loading';
    }

    const isSuperUser = (userProfile as any)?.role === 'SuperUser';
    
    // For SuperUsers, determine interface mode based on contractor profile
    // This allows SuperUsers to test both GC and Contractor interfaces
    const getInterfaceMode = () => {
      if (!contractorProfile) {
        return 'general_contractor';
      }

      try {
        const profile = contractorProfile as any;
        
        // Check trade types to determine interface mode
        const tradeTypes = profile.tradeTypes || [];
        if (Array.isArray(tradeTypes) && tradeTypes.length > 0) {
          // If they have "General Contractor" in their trade types, show GC interface
          const hasGeneralContractor = tradeTypes.some(trade => 
            trade === 'General Contractor' || trade === 'general_contractor'
          );
          
          if (hasGeneralContractor) {
            return 'general_contractor';
          } else {
            // If they have other trades but no "General Contractor", show contractor interface
            return 'contractor';
          }
        }

        return 'general_contractor';
      } catch (error) {
        console.warn('Error determining interface mode:', error);
        return 'general_contractor';
      }
    };
    
    // SuperUsers get SuperUser privileges but interface is determined by contractor profile
    if (isSuperUser) {
      return 'SuperUser';
    }

    // If no contractor profile exists, assume they are a General Contractor
    if (!contractorProfile) {
      return 'general_contractor';
    }

    try {
      const profile = contractorProfile as any;
      
      // Check trade types to determine role
      const tradeTypes = profile.tradeTypes || [];
      if (Array.isArray(tradeTypes) && tradeTypes.length > 0) {
        // If they have "General Contractor" in their trade types, they're a GC
        // regardless of other specialty trades they might also have
        const hasGeneralContractor = tradeTypes.some(trade => 
          trade === 'General Contractor' || trade === 'general_contractor'
        );
        
        if (hasGeneralContractor) {
          return 'general_contractor';
        } else {
          // If they have other trades but no "General Contractor", they're a specialty contractor
          return 'contractor';
        }
      }

      // Default to General Contractor if no trade types are set
      return 'general_contractor';
    } catch (error) {
      console.warn('Error determining user role:', error);
      return 'general_contractor';
    }
  };

  const role = getUserRole();
  const isSuperUser = (userProfile as any)?.role === 'SuperUser';
  
  // For interface purposes, determine what interface to show
  const getInterfaceMode = () => {
    if (!contractorProfile) {
      return 'general_contractor';
    }

    try {
      const profile = contractorProfile as any;
      const tradeTypes = profile.tradeTypes || [];
      if (Array.isArray(tradeTypes) && tradeTypes.length > 0) {
        const hasGeneralContractor = tradeTypes.some(trade => 
          trade === 'General Contractor' || trade === 'general_contractor'
        );
        
        return hasGeneralContractor ? 'general_contractor' : 'contractor';
      }
      return 'general_contractor';
    } catch (error) {
      return 'general_contractor';
    }
  };

  const interfaceMode = getInterfaceMode();

  return {
    role,
    interfaceMode, // New: separate interface mode for SuperUsers
    isLoading: isProfileLoading || isUserLoading || !isAuthenticated,
    contractorProfile,
    userProfile,
    isGeneralContractor: interfaceMode === 'general_contractor',
    isContractor: interfaceMode === 'contractor',
    isSuperUser,
  };
}
import { Request } from 'express';
import { storage } from '../storage';
import { InsertBusinessAuditLog } from '@shared/schema';

// Event data type definitions
export interface FileUploadEventData {
  fileName: string;
  fileSize: number;
  fileType: string;
  objectKey?: string;
  rfqId?: string;
  bidId?: string;
}

export interface BidActionEventData {
  bidId: string;
  contractorId: string;
  contractorName: string;
  bidAmount: number | null;
  action: string;
  previousStatus: string;
  newStatus: string;
}

export interface RfqCreationEventData {
  rfqId: string;
  projectName: string;
  tradeCategory?: string;
  dueDate?: Date;
}

export interface RfqDistributionEventData {
  rfqId: string;
  projectName: string;
  distributionMethod: string; // 'favorites' | 'broadcast'
  contractorCount: number;
  contractorIds: string[];
}

export class AuditService {
  
  /**
   * Log file upload events
   */
  static async logFileUpload({
    userId,
    fileName,
    fileSize,
    fileType,
    objectKey,
    rfqId,
    bidId,
    req
  }: {
    userId: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    objectKey?: string;
    rfqId?: string;
    bidId?: string;
    req?: Request;
  }): Promise<void> {
    try {
      const eventData: FileUploadEventData = {
        fileName,
        fileSize,
        fileType,
        objectKey,
        rfqId,
        bidId
      };

      const auditEntry: InsertBusinessAuditLog = {
        userId,
        eventType: 'file_upload',
        eventData,
        resourceId: rfqId || bidId || objectKey,
        ipAddress: req?.ip || req?.socket?.remoteAddress,
        userAgent: req?.get('User-Agent')
      };

      await storage.createBusinessAuditLog(auditEntry);
      console.log(`📝 AUDIT: File upload logged - ${fileName} (${fileSize} bytes) by user ${userId}`);
    } catch (error) {
      console.error('Error logging file upload audit:', error);
    }
  }

  /**
   * Log bid acceptance/rejection events
   */
  static async logBidAction({
    userId,
    bidId,
    contractorId,
    contractorName,
    bidAmount,
    action,
    previousStatus,
    newStatus,
    req
  }: {
    userId: string;
    bidId: string;
    contractorId: string;
    contractorName: string;
    bidAmount: number | null;
    action: string;
    previousStatus: string;
    newStatus: string;
    req?: Request;
  }): Promise<void> {
    try {
      const eventData: BidActionEventData = {
        bidId,
        contractorId,
        contractorName,
        bidAmount,
        action,
        previousStatus,
        newStatus
      };

      const auditEntry: InsertBusinessAuditLog = {
        userId,
        eventType: 'bid_action',
        eventData,
        resourceId: bidId,
        ipAddress: req?.ip || req?.socket?.remoteAddress,
        userAgent: req?.get('User-Agent')
      };

      await storage.createBusinessAuditLog(auditEntry);
      console.log(`📝 AUDIT: Bid action logged - ${action} for bid ${bidId} (${bidAmount ? `$${bidAmount.toLocaleString()}` : 'TBD'}) by user ${userId}`);
    } catch (error) {
      console.error('Error logging bid action audit:', error);
    }
  }

  /**
   * Log RFQ creation events
   */
  static async logRfqCreation({
    userId,
    rfqId,
    projectName,
    tradeCategory,
    dueDate,
    req
  }: {
    userId: string;
    rfqId: string;
    projectName: string;
    tradeCategory?: string;
    dueDate?: Date;
    req?: Request;
  }): Promise<void> {
    try {
      const eventData: RfqCreationEventData = {
        rfqId,
        projectName,
        tradeCategory,
        dueDate
      };

      const auditEntry: InsertBusinessAuditLog = {
        userId,
        eventType: 'rfq_creation',
        eventData,
        resourceId: rfqId,
        ipAddress: req?.ip || req?.socket?.remoteAddress,
        userAgent: req?.get('User-Agent')
      };

      await storage.createBusinessAuditLog(auditEntry);
      console.log(`📝 AUDIT: RFQ creation logged - ${projectName} (${rfqId}) by user ${userId}`);
    } catch (error) {
      console.error('Error logging RFQ creation audit:', error);
    }
  }

  /**
   * Log RFQ distribution events
   */
  static async logRfqDistribution({
    userId,
    rfqId,
    projectName,
    distributionMethod,
    contractorCount,
    contractorIds,
    req
  }: {
    userId: string;
    rfqId: string;
    projectName: string;
    distributionMethod: string;
    contractorCount: number;
    contractorIds: string[];
    req?: Request;
  }): Promise<void> {
    try {
      const eventData: RfqDistributionEventData = {
        rfqId,
        projectName,
        distributionMethod,
        contractorCount,
        contractorIds
      };

      const auditEntry: InsertBusinessAuditLog = {
        userId,
        eventType: 'rfq_distribution',
        eventData,
        resourceId: rfqId,
        ipAddress: req?.ip || req?.socket?.remoteAddress,
        userAgent: req?.get('User-Agent')
      };

      await storage.createBusinessAuditLog(auditEntry);
      console.log(`📝 AUDIT: RFQ distribution logged - ${projectName} to ${contractorCount} contractors via ${distributionMethod} by user ${userId}`);
    } catch (error) {
      console.error('Error logging RFQ distribution audit:', error);
    }
  }

  /**
   * Get business audit logs with optional filtering
   */
  static async getBusinessAuditLogs({
    eventType,
    userId,
    resourceId,
    startDate,
    endDate,
    limit = 100
  }: {
    eventType?: string;
    userId?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  } = {}) {
    try {
      return await storage.getBusinessAuditLogs({
        eventType,
        userId,
        resourceId,
        startDate,
        endDate,
        limit
      });
    } catch (error) {
      console.error('Error fetching business audit logs:', error);
      return [];
    }
  }
}
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ThemeProvider";
import { useAuth } from "@/hooks/useAuth";
import TermsProvider from "@/providers/TermsProvider";
import { Navbar } from "@/components/Navbar";
import { Sidebar } from "@/components/Sidebar";
import ErrorBoundary from "@/components/ErrorBoundary";
import NotFound from "@/pages/not-found";
import Landing from "@/pages/Landing";
import Dashboard from "@/pages/Dashboard";
import RFQs from "@/pages/RFQs";
import Contractors from "@/pages/Contractors";
import Settings from "@/pages/Settings";
import HelpSupport from "@/pages/HelpSupport";
import Templates from "@/pages/Templates";
import BidResponses from "@/pages/BidResponses";
import BidManagement from "@/pages/BidManagement";
import RfqBidManagement from "@/pages/RfqBidManagement";
import ContractorRfqs from "@/pages/ContractorRfqs";
import RfqDetail from "@/pages/RfqDetail";
import FavoritesManagement from "@/pages/FavoritesManagement";
import Analytics from "@/pages/Analytics";
import MatIQ from "@/pages/Forecast";
import DocumentLibrary from "@/pages/DocumentLibrary";
import { useEffect } from "react";

function Router() {
  const { isAuthenticated } = useAuth();

  return (
    <Switch>
      {!isAuthenticated ? (
        <Route path="/" component={Landing} />
      ) : (
        <>
          <Route path="/" component={Dashboard} />
          <Route path="/rfqs" component={RFQs} />
          <Route path="/rfq/:rfqId" component={RfqDetail} />
          <Route path="/rfqs/:rfqId/bids" component={RfqBidManagement} />
          <Route path="/contractors" component={Contractors} />
          <Route path="/contractors/favorites" component={FavoritesManagement} />
          <Route path="/contractors/rfqs" component={ContractorRfqs} />
          <Route path="/bids" component={BidManagement} />
          <Route path="/bid-responses/:rfqId" component={BidResponses} />
          <Route path="/analytics" component={Analytics} />
          <Route path="/forecast" component={MatIQ} />
          <Route path="/documents" component={DocumentLibrary} />
          <Route path="/settings" component={Settings} />
          <Route path="/help" component={HelpSupport} />
          <Route path="/templates" component={Templates} />
          <Route component={NotFound} />
        </>
      )}
      <Route component={NotFound} />
    </Switch>
  );
}

function AuthenticatedApp() {
  const { isAuthenticated, isLoading } = useAuth();

  console.log("🔄 AuthenticatedApp: Render state", { isAuthenticated, isLoading });

  if (isLoading) {
    console.log("⏳ AuthenticatedApp: Showing loading state");
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-primary-foreground font-bold text-lg">B</span>
          </div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  console.log("🎯 AuthenticatedApp: Rendering main app", { isAuthenticated });

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      {isAuthenticated ? (
        <div className="flex">
          <Sidebar />
          <main className="flex-1 p-6">
            <Router />
          </main>
        </div>
      ) : (
        <main className="flex-1">
          <Router />
        </main>
      )}
    </div>
  );
}

function App() {
  console.log("🚀 App component rendering");

  // Handle runtime errors globally
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error("🚨 Runtime error caught:", event.error);
      // Prevent the error from causing a white screen
      event.preventDefault();
      return true;
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error("🚨 Unhandled promise rejection:", event.reason);
      // Prevent unhandled rejections from crashing the app
      event.preventDefault();
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
        <ErrorBoundary>
          <TermsProvider>
            <AuthenticatedApp />
            <Toaster />
          </TermsProvider>
        </ErrorBoundary>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
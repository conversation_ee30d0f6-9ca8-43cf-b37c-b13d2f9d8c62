import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { nanoid } from 'nanoid';
import { storage } from '../storage';
import type { InsertApiKey } from '@shared/schema';

const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';
const API_KEY_PREFIX = 'bda_';

// Enhanced security configuration
const SECURITY_CONFIG = {
  MAX_FAILED_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  ROTATION_WARNING_DAYS: 30,
  SUSPICIOUS_USAGE_THRESHOLD: 1000, // requests per hour
};

export interface ApiKeyData {
  id: string;
  userId: string;
  name: string;
  permissions: string;
  rateLimit: number;
  isActive?: boolean;
  expiresAt?: Date;
  lastUsedAt?: Date;
  createdAt?: Date;
  allowedIPs?: string[];
  environment?: 'development' | 'staging' | 'production';
}

export interface ApiKeySecurityInfo {
  failedAttempts: number;
  lastFailedAt?: Date;
  isLocked: boolean;
  lockoutUntil?: Date;
  suspiciousActivity: boolean;
  lastRotated?: Date;
}

export async function generateApiKey(
  userId: string,
  name: string,
  permissions: 'read-only' | 'upload-only' | 'full-access' = 'read-only',
  rateLimit: number = 100,
  options: {
    expirationDays?: number;
    allowedIPs?: string[];
    environment?: 'development' | 'staging' | 'production';
  } = {}
): Promise<{ apiKey: string; keyRecord: any; securityToken: string }> {
  const keyId = nanoid();
  const expirationDays = options.expirationDays || 365;
  
  // Create enhanced JWT payload
  const payload: ApiKeyData = {
    id: keyId,
    userId,
    name,
    permissions,
    rateLimit,
    allowedIPs: options.allowedIPs,
    environment: options.environment || 'production'
  };
  
  // Generate JWT token with enhanced security
  const token = jwt.sign(payload, JWT_SECRET, {
    expiresIn: `${expirationDays}d`,
    issuer: 'bidaible-api',
    subject: userId,
    algorithm: 'HS256'
  });
  
  // Create API key with prefix
  const apiKey = API_KEY_PREFIX + token;
  
  // Generate security token for key management operations
  const securityTokenPayload = {
    keyId,
    operation: 'management',
    createdAt: Date.now()
  };
  const securityToken = jwt.sign(securityTokenPayload, JWT_SECRET, {
    expiresIn: '24h',
    issuer: 'bidaible-security'
  });
  
  // Hash the key for storage (using stronger hashing)
  const keyHash = crypto.createHash('sha256').update(apiKey + process.env.SALT || 'bidaible-salt').digest('hex');
  
  // Store in database with enhanced security fields
  const keyRecord = await storage.createApiKey({
    id: keyId,
    userId,
    name,
    keyHash,
    permissions,
    rateLimit,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastUsedAt: null,
    expiresAt: new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000)
  });
  
  // Log key creation for security audit
  console.log(`🔐 API Key Security: Generated new key '${name}' for user ${userId} with ${permissions} permissions`);
  
  return { apiKey, keyRecord, securityToken };
}

// Security monitoring store (in production, use Redis or database)
const securityStore = new Map<string, ApiKeySecurityInfo>();

export async function verifyApiKey(apiKey: string, clientIP?: string): Promise<ApiKeyData | null> {
  try {
    // Check if it has the correct prefix
    if (!apiKey.startsWith(API_KEY_PREFIX)) {
      await recordFailedAttempt(apiKey, 'invalid_prefix', clientIP);
      return null;
    }
    
    // Extract token without prefix
    const token = apiKey.substring(API_KEY_PREFIX.length);
    
    // Verify JWT with enhanced security
    const payload = jwt.verify(token, JWT_SECRET, {
      issuer: 'bidaible-api',
      algorithms: ['HS256']
    }) as ApiKeyData;
    
    // Enhanced hash verification
    const keyHash = crypto.createHash('sha256').update(apiKey + process.env.SALT || 'bidaible-salt').digest('hex');
    
    // Check if key exists and is active
    const keyRecord = await storage.getApiKeyByHash(keyHash);
    
    if (!keyRecord || !keyRecord.isActive) {
      await recordFailedAttempt(apiKey, 'key_not_found', clientIP);
      return null;
    }

    // Check security constraints
    const securityInfo = getKeySecurityInfo(keyRecord.id);
    
    // Check if key is locked due to suspicious activity
    if (securityInfo.isLocked && securityInfo.lockoutUntil && new Date() < securityInfo.lockoutUntil) {
      await recordFailedAttempt(apiKey, 'key_locked', clientIP);
      return null;
    }

    // Check IP restrictions if configured
    if (payload.allowedIPs && payload.allowedIPs.length > 0 && clientIP) {
      if (!payload.allowedIPs.includes(clientIP)) {
        await recordFailedAttempt(apiKey, 'ip_restriction', clientIP);
        console.log(`🚨 API Key Security: IP restriction violation - Key: ${payload.name}, IP: ${clientIP}`);
        return null;
      }
    }
    
    // Check if key is expired
    if (keyRecord.expiresAt && new Date() > keyRecord.expiresAt) {
      // Deactivate expired key
      await storage.updateApiKey?.(keyRecord.id, { isActive: false });
      await recordFailedAttempt(apiKey, 'key_expired', clientIP);
      return null;
    }

    // Update last used timestamp and reset failed attempts on successful verification
    await storage.updateApiKeyLastUsed(keyRecord.id);
    resetFailedAttempts(keyRecord.id);
    
    return {
      ...payload,
      isActive: keyRecord.isActive,
      expiresAt: keyRecord.expiresAt,
      lastUsedAt: keyRecord.lastUsedAt,
      createdAt: keyRecord.createdAt
    };
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      await recordFailedAttempt(apiKey, 'token_expired', clientIP);
    } else if (error instanceof jwt.JsonWebTokenError) {
      await recordFailedAttempt(apiKey, 'invalid_token', clientIP);
    } else {
      console.error('API key verification error:', error);
      await recordFailedAttempt(apiKey, 'verification_error', clientIP);
    }
    return null;
  }
}

export async function getApiKeyStats(apiKeyId: string): Promise<{
  totalRequests: number;
  requestsToday: number;
  requestsThisMonth: number;
  lastUsed: Date | null;
  topEndpoints: Array<{ endpoint: string; requests: number }>;
  dailyUsage: Array<{ date: string; requests: number }>;
}> {
  const usageData = await storage.getApiKeyUsageStats(apiKeyId);
  const apiKey = await storage.getApiKey(apiKeyId);
  
  const today = new Date().toISOString().split('T')[0];
  const thisMonth = new Date().toISOString().substring(0, 7);
  
  const totalRequests = usageData.reduce((sum, usage) => sum + usage.requestCount, 0);
  const requestsToday = usageData
    .filter(usage => usage.requestDate === today)
    .reduce((sum, usage) => sum + usage.requestCount, 0);
  const requestsThisMonth = usageData
    .filter(usage => usage.requestDate.startsWith(thisMonth))
    .reduce((sum, usage) => sum + usage.requestCount, 0);
  
  // Group by endpoint to get top endpoints
  const endpointMap = new Map<string, number>();
  usageData.forEach(usage => {
    const current = endpointMap.get(usage.endpoint) || 0;
    endpointMap.set(usage.endpoint, current + usage.requestCount);
  });
  
  const topEndpoints = Array.from(endpointMap.entries())
    .map(([endpoint, requests]) => ({ endpoint, requests }))
    .sort((a, b) => b.requests - a.requests)
    .slice(0, 10);
  
  // Group by date for daily usage
  const dailyMap = new Map<string, number>();
  usageData.forEach(usage => {
    const current = dailyMap.get(usage.requestDate) || 0;
    dailyMap.set(usage.requestDate, current + usage.requestCount);
  });
  
  const dailyUsage = Array.from(dailyMap.entries())
    .map(([date, requests]) => ({ date, requests }))
    .sort((a, b) => a.date.localeCompare(b.date))
    .slice(-30); // Last 30 days
  
  return {
    totalRequests,
    requestsToday,
    requestsThisMonth,
    lastUsed: apiKey?.lastUsedAt || null,
    topEndpoints,
    dailyUsage
  };
}

export async function revokeApiKey(apiKeyId: string): Promise<boolean> {
  return await storage.deleteApiKey(apiKeyId);
}

// Enhanced security functions
export async function recordFailedAttempt(apiKey: string, reason: string, clientIP?: string): Promise<void> {
  try {
    // Extract key ID if possible
    let keyId = 'unknown';
    try {
      if (apiKey.startsWith(API_KEY_PREFIX)) {
        const token = apiKey.substring(API_KEY_PREFIX.length);
        const payload = jwt.decode(token) as any;
        keyId = payload?.id || 'unknown';
      }
    } catch {
      // Key might be malformed, continue with 'unknown'
    }

    const securityInfo = securityStore.get(keyId) || {
      failedAttempts: 0,
      isLocked: false,
      suspiciousActivity: false
    };

    securityInfo.failedAttempts++;
    securityInfo.lastFailedAt = new Date();

    // Lock key if too many failed attempts
    if (securityInfo.failedAttempts >= SECURITY_CONFIG.MAX_FAILED_ATTEMPTS) {
      securityInfo.isLocked = true;
      securityInfo.lockoutUntil = new Date(Date.now() + SECURITY_CONFIG.LOCKOUT_DURATION);
      console.log(`🔒 API Key Security: Key ${keyId} locked due to ${securityInfo.failedAttempts} failed attempts`);
    }

    securityStore.set(keyId, securityInfo);

    // Log security event
    console.log(`🚨 API Key Security: Failed attempt - Key: ${keyId}, Reason: ${reason}, IP: ${clientIP || 'unknown'}`);
  } catch (error) {
    console.error('Failed to record security event:', error);
  }
}

export function getKeySecurityInfo(keyId: string): ApiKeySecurityInfo {
  return securityStore.get(keyId) || {
    failedAttempts: 0,
    isLocked: false,
    suspiciousActivity: false
  };
}

export function resetFailedAttempts(keyId: string): void {
  const securityInfo = securityStore.get(keyId);
  if (securityInfo) {
    securityInfo.failedAttempts = 0;
    securityInfo.isLocked = false;
    securityInfo.lockoutUntil = undefined;
    securityStore.set(keyId, securityInfo);
  }
}

// API Key rotation functionality
export async function rotateApiKey(
  currentApiKey: string, 
  securityToken: string,
  newOptions?: {
    name?: string;
    permissions?: 'read-only' | 'upload-only' | 'full-access';
    rateLimit?: number;
    expirationDays?: number;
  }
): Promise<{ newApiKey: string; newSecurityToken: string } | null> {
  try {
    // Verify security token
    const tokenPayload = jwt.verify(securityToken, JWT_SECRET, {
      issuer: 'bidaible-security'
    }) as any;

    // Verify current API key
    const currentKeyData = await verifyApiKey(currentApiKey);
    if (!currentKeyData || currentKeyData.id !== tokenPayload.keyId) {
      return null;
    }

    // Generate new API key with same or updated settings
    const result = await generateApiKey(
      currentKeyData.userId,
      newOptions?.name || currentKeyData.name,
      newOptions?.permissions || currentKeyData.permissions as any,
      newOptions?.rateLimit || currentKeyData.rateLimit,
      {
        expirationDays: newOptions?.expirationDays,
        allowedIPs: currentKeyData.allowedIPs,
        environment: currentKeyData.environment
      }
    );

    // Deactivate old key (don't delete for audit trail)
    const oldKeyHash = crypto.createHash('sha256').update(currentApiKey + process.env.SALT || 'bidaible-salt').digest('hex');
    const oldKeyRecord = await storage.getApiKeyByHash(oldKeyHash);
    if (oldKeyRecord) {
      await storage.updateApiKey?.(oldKeyRecord.id, { isActive: false });
    }

    // Update security info
    const securityInfo = getKeySecurityInfo(currentKeyData.id);
    securityInfo.lastRotated = new Date();
    securityStore.set(result.keyRecord.id, securityInfo);

    console.log(`🔄 API Key Security: Rotated key '${currentKeyData.name}' for user ${currentKeyData.userId}`);

    return {
      newApiKey: result.apiKey,
      newSecurityToken: result.securityToken
    };
  } catch (error) {
    console.error('API key rotation failed:', error);
    return null;
  }
}

// Check for keys that need rotation
export async function getKeysNeedingRotation(userId: string): Promise<Array<{
  id: string;
  name: string;
  daysSinceCreation: number;
  lastUsed?: Date;
}>> {
  const userKeys = await storage.getApiKeysByUserId(userId);
  const keysNeedingRotation = [];

  for (const key of userKeys) {
    if (!key.isActive || !key.createdAt) continue;

    const daysSinceCreation = Math.floor(
      (Date.now() - key.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceCreation > SECURITY_CONFIG.ROTATION_WARNING_DAYS) {
      keysNeedingRotation.push({
        id: key.id,
        name: key.name,
        daysSinceCreation,
        lastUsed: key.lastUsedAt
      });
    }
  }

  return keysNeedingRotation;
}

// Enhanced API key statistics
export async function getAdvancedApiKeyStats(): Promise<{
  totalActiveKeys: number;
  keysNeedingRotation: number;
  suspiciousActivity: number;
  lockedKeys: number;
  topUsers: Array<{ userId: string; keyCount: number }>;
}> {
  // Note: This would require getAllApiKeys method in storage interface
  // For now, return basic stats from security store
  const activeKeys: any[] = [];
  let totalActiveKeys = 0;
  
  let keysNeedingRotation = 0;
  let suspiciousActivity = 0;
  let lockedKeys = 0;

  const userKeyCount = new Map<string, number>();

  // Count from security store
  for (const [keyId, securityInfo] of securityStore.entries()) {
    if (securityInfo.suspiciousActivity) suspiciousActivity++;
    if (securityInfo.isLocked) lockedKeys++;
  }

  const topUsers = Array.from(userKeyCount.entries())
    .map(([userId, keyCount]) => ({ userId, keyCount }))
    .sort((a, b) => b.keyCount - a.keyCount)
    .slice(0, 10);

  return {
    totalActiveKeys: securityStore.size,
    keysNeedingRotation,
    suspiciousActivity,
    lockedKeys,
    topUsers
  };
}

export async function updateApiKeySettings(
  apiKeyId: string,
  updates: { name?: string; isActive?: boolean; rateLimit?: number }
): Promise<any> {
  return await storage.updateApiKey(apiKeyId, updates);
}

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { MapPin, Phone, Mail, Award, Star, Users, Calendar, Building2 } from "lucide-react";
import { getQueryFn } from "@/lib/queryClient";

interface ProfileModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: any;
}

export function ProfileModal({ open, onOpenChange, user }: ProfileModalProps) {
  const { data: contractorProfile } = useQuery({
    queryKey: ["/api/contractors/profile"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: open && !!user,
  });

  if (!user) return null;

  const getInitials = () => {
    return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`;
  };

  const formatTradeTypes = (tradeTypes: string[]) => {
    if (!tradeTypes || tradeTypes.length === 0) return [];
    return tradeTypes.map(trade => {
      return trade.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="sr-only">Profile Overview</DialogTitle>
        </DialogHeader>
        
        <Card className="border-0 shadow-none">
          <CardContent className="p-6 space-y-4">
            {/* Header Section */}
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={user?.profileImageUrl || ""} />
                  <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                    {getInitials()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-lg font-semibold">
                    {contractorProfile?.companyName || `${user.firstName} ${user.lastName}`}
                  </h3>
                  {contractorProfile?.businessAddress && (
                    <div className="flex items-center text-sm text-muted-foreground mt-1">
                      <MapPin className="h-3 w-3 mr-1" />
                      <span className="line-clamp-1">
                        {contractorProfile.businessAddress.split(',')[0]}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              {contractorProfile && (
                <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                  ✓ Approved
                </Badge>
              )}
            </div>

            {/* Rating Section */}
            {contractorProfile && (
              <div className="flex items-center space-x-2">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">4.8</span>
                <span className="text-sm text-muted-foreground">(187 projects)</span>
              </div>
            )}

            {/* Badges Section */}
            {contractorProfile && (
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50 dark:bg-blue-900/20">
                  <Building2 className="h-3 w-3 mr-1" />
                  Prime Contractor
                </Badge>
                {contractorProfile.unionStatus === 'union' && (
                  <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50 dark:bg-green-900/20">
                    <Users className="h-3 w-3 mr-1" />
                    Union
                  </Badge>
                )}
              </div>
            )}

            {/* Trade Types */}
            {contractorProfile?.tradeTypes && contractorProfile.tradeTypes.length > 0 && (
              <div className="space-y-2">
                <div className="flex flex-wrap gap-1">
                  {formatTradeTypes(contractorProfile.tradeTypes).slice(0, 3).map((trade, index) => (
                    <Badge key={index} variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 text-xs">
                      {trade}
                    </Badge>
                  ))}
                  {contractorProfile.tradeTypes.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{contractorProfile.tradeTypes.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            )}

            <Separator />

            {/* Contact Information */}
            <div className="space-y-3">
              {contractorProfile?.contactPhone && (
                <div className="flex items-center text-sm">
                  <Phone className="h-4 w-4 mr-3 text-muted-foreground" />
                  <span>{contractorProfile.contactPhone}</span>
                </div>
              )}
              
              <div className="flex items-center text-sm">
                <Mail className="h-4 w-4 mr-3 text-muted-foreground" />
                <span>{contractorProfile?.contactEmail || user.email}</span>
              </div>

              {contractorProfile?.licenseNumber && (
                <div className="flex items-center text-sm">
                  <Award className="h-4 w-4 mr-3 text-muted-foreground" />
                  <span>License: {contractorProfile.licenseNumber}</span>
                </div>
              )}
            </div>

            {/* Experience & Employees */}
            {contractorProfile && (contractorProfile.yearsInBusiness || contractorProfile.employeeCount) && (
              <>
                <Separator />
                <div className="grid grid-cols-2 gap-4">
                  {contractorProfile.yearsInBusiness && (
                    <div>
                      <div className="text-sm text-muted-foreground">Experience:</div>
                      <div className="font-medium">{contractorProfile.yearsInBusiness} years</div>
                    </div>
                  )}
                  {contractorProfile.employeeCount && (
                    <div>
                      <div className="text-sm text-muted-foreground">Employees:</div>
                      <div className="font-medium">{contractorProfile.employeeCount}</div>
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}

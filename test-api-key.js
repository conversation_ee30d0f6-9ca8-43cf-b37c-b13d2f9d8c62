#!/usr/bin/env node

/**
 * Test script for API Key Authentication System
 * This script demonstrates how to use the new API key endpoints
 */

const API_BASE_URL = 'http://localhost:5000/api';

// Test API key authentication and usage
async function testApiKeySystem() {
    console.log('🔑 Testing API Key Authentication System\n');
    
    // Step 1: Create an API key using session authentication
    console.log('1. Creating API key with session authentication...');
    
    const createKeyResponse = await fetch(`${API_BASE_URL}/auth/api-keys`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            // In a real scenario, you'd include session cookies here
            'Cookie': 'session=your-session-cookie'
        },
        body: JSON.stringify({
            name: 'Test API Key',
            permissions: 'read-only',
            rateLimit: 50
        })
    });
    
    if (!createKeyResponse.ok) {
        console.error('❌ Failed to create API key:', await createKeyResponse.text());
        return;
    }
    
    const newApiKey = await createKeyResponse.json();
    console.log('✅ API key created successfully');
    console.log(`   ID: ${newApiKey.id}`);
    console.log(`   Name: ${newApiKey.name}`);
    console.log(`   Permissions: ${newApiKey.permissions}`);
    console.log(`   Rate Limit: ${newApiKey.rateLimit} requests/hour`);
    console.log(`   API Key: ${newApiKey.apiKey.substring(0, 20)}...`);
    
    // Step 2: Test API key authentication
    console.log('\n2. Testing API key authentication...');
    
    const rfqsResponse = await fetch(`${API_BASE_URL}/rfqs`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${newApiKey.apiKey}`
        }
    });
    
    if (rfqsResponse.ok) {
        const rfqs = await rfqsResponse.json();
        console.log(`✅ Successfully fetched ${rfqs.length} RFQs using API key`);
        
        // Check rate limit headers
        const rateLimitHeaders = {
            limit: rfqsResponse.headers.get('X-RateLimit-Limit'),
            remaining: rfqsResponse.headers.get('X-RateLimit-Remaining'),
            reset: rfqsResponse.headers.get('X-RateLimit-Reset')
        };
        console.log('   Rate limit info:', rateLimitHeaders);
    } else {
        console.error('❌ Failed to fetch RFQs:', await rfqsResponse.text());
    }
    
    // Step 3: Test permission restrictions
    console.log('\n3. Testing permission restrictions...');
    
    const uploadResponse = await fetch(`${API_BASE_URL}/rfqs`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${newApiKey.apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            projectName: 'Test Project',
            projectLocation: 'Test Location',
            tradeCategory: 'general',
            description: 'Test description'
        })
    });
    
    if (uploadResponse.status === 403) {
        console.log('✅ Permission restriction working correctly (403 Forbidden)');
    } else {
        console.error('❌ Permission restriction failed:', await uploadResponse.text());
    }
    
    // Step 4: Test integration endpoints
    console.log('\n4. Testing integration endpoints...');
    
    const quickbooksResponse = await fetch(`${API_BASE_URL}/integrations/quickbooks`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${newApiKey.apiKey}`
        }
    });
    
    if (quickbooksResponse.ok) {
        const quickbooksData = await quickbooksResponse.json();
        console.log('✅ QuickBooks integration endpoint working');
        console.log(`   Export contains ${quickbooksData.financialData.length} projects`);
    } else {
        console.error('❌ QuickBooks integration failed:', await quickbooksResponse.text());
    }
    
    // Step 5: Test CSV export
    console.log('\n5. Testing CSV export...');
    
    const csvResponse = await fetch(`${API_BASE_URL}/integrations/export/rfqs?format=csv`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${newApiKey.apiKey}`
        }
    });
    
    if (csvResponse.ok) {
        const csvContent = await csvResponse.text();
        console.log('✅ CSV export working');
        console.log(`   CSV content: ${csvContent.split('\n').length} lines`);
    } else {
        console.error('❌ CSV export failed:', await csvResponse.text());
    }
    
    console.log('\n🎉 API Key system test completed!');
}

// Example usage with curl commands
function showCurlExamples() {
    console.log('\n📋 Example curl commands for API key usage:\n');
    
    console.log('# Create API key (requires session authentication)');
    console.log('curl -X POST http://localhost:5000/api/auth/api-keys \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -H "Cookie: session=your-session-cookie" \\');
    console.log('  -d \'{"name": "My API Key", "permissions": "full-access", "rateLimit": 100}\'');
    
    console.log('\n# Use API key to fetch RFQs');
    console.log('curl -X GET http://localhost:5000/api/rfqs \\');
    console.log('  -H "Authorization: Bearer bda_your-api-key-here"');
    
    console.log('\n# Upload RFQ with API key');
    console.log('curl -X POST http://localhost:5000/api/rfqs \\');
    console.log('  -H "Authorization: Bearer bda_your-api-key-here" \\');
    console.log('  -F "projectName=Test Project" \\');
    console.log('  -F "projectLocation=Test Location" \\');
    console.log('  -F "tradeCategory=general" \\');
    console.log('  -F "description=Test description" \\');
    console.log('  -F "documents=@/path/to/document.pdf"');
    
    console.log('\n# Export data for QuickBooks');
    console.log('curl -X GET http://localhost:5000/api/integrations/quickbooks \\');
    console.log('  -H "Authorization: Bearer bda_your-api-key-here"');
    
    console.log('\n# Export RFQs as CSV');
    console.log('curl -X GET "http://localhost:5000/api/integrations/export/rfqs?format=csv" \\');
    console.log('  -H "Authorization: Bearer bda_your-api-key-here"');
    
    console.log('\n# Sync with Sage ERP');
    console.log('curl -X POST http://localhost:5000/api/integrations/sage \\');
    console.log('  -H "Authorization: Bearer bda_your-api-key-here" \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"projectData": [{"projectName": "Project 1", "projectLocation": "Location 1"}]}\'');
}

// Run the tests if called directly
if (require.main === module) {
    console.log('Note: This test requires a running server and valid session cookies.');
    console.log('For demonstration purposes, here are the curl examples:\n');
    showCurlExamples();
}

module.exports = { testApiKeySystem, showCurlExamples };
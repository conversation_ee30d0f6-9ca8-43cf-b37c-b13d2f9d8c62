Phase 1: Initial RFQ Receipt & Upload

Given I receive an RFQ package from the local municipality via email with multiple attachments (overview document, spec sheets, architectural drawings, electrical plans, addenda)

When I navigate to my file library

Then I can drag-and-drop or select all RFQ files for upload at once

When files finish uploading

Then each file automatically enters AI processing and I see status indicators ("Processing..." ? "Ready")

When AI processing completes

Then I can view individual AI summaries for each file showing:

Document type (overview, specifications, drawings, addenda)

Key requirements and scope items

Important dates, quantities, and technical details

Extracted project information

Phase 2: RFQ Package Assembly

When a new RFQ package is created

Then I can browse my file library and select all related municipal files

When I associate the files with the RFQ

Then the system automatically generates a master consolidated summary that:

Combines insights from all individual file summaries

Identifies the overall project scope and requirements

Highlights key dates, budget considerations, and technical specifications

Flags any conflicting information between documents

When I view the RFQ dashboard

Then I see both the master summary and can drill down to individual file summaries

Phase 3: Subcontractor Quote Management

When I need to send the RFQ package to different subcontractors

Then I can:

Select all or selected RFQ-related files

When subcontractors upload and submit their bids back to the RFQ

They are associated with the same RFQ ID

When all subcontractor bids are received and processed

The General Contractor reviews the bid and will Accept, Reject or Request More Info

If Accepted, the subcontractor is notified and the bid information is added to the dashboard

If Rejected, the subcontractor is notified using an AI generated message that is emailed.

Then the master RFQ summary updates to include:

Summary of all bids, with the ability to filter bids by status (in review, accepted, rejected, request more info)

Comparison of subcontractor pricing and approaches, what is included and not included

Complete RFQ overview with both original requirements and proposed solutions

Phase 4: Complete Package View

When I need to review the entire RFQ package

Then I have a single dashboard view showing:

Original RFQ documents and their summaries

Master RFQ summary with all requirements

All subcontractor bids and their summaries, with the ability to filter the bids that have been accepted

Consolidated analysis of the complete package

When I need to prepare my final bid response

Then I have all information organized and summarized for efficient proposal preparation

A report is generated that includes all subcontract bid summaries.
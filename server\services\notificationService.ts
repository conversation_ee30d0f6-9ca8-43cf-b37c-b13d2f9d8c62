/**
 * Notification Service - Core business logic for the notification system
 * Handles notification creation, delivery, and preference management
 */

import { storage } from '../storage';
import { emailService } from './emailService';
import type { 
  InsertNotification, 
  InsertNotificationDelivery, 
  NotificationPreferences 
} from '@shared/schema';

export class NotificationService {
  
  /**
   * Log email events to business audit log
   */
  private static async logEmailEvent(
    userId: string, 
    eventType: string, 
    emailData: any,
    request?: any
  ) {
    try {
      await storage.createBusinessAuditLog({
        userId,
        eventType,
        eventData: emailData,
        resourceId: emailData.notificationId || emailData.messageId,
        ipAddress: request?.ip || request?.connection?.remoteAddress,
        userAgent: request?.get?.('User-Agent'),
      });
      console.log(`📝 Logged email event: ${eventType} for user ${userId}`);
    } catch (error) {
      console.error(`❌ Failed to log email event ${eventType}:`, error);
    }
  }

  /**
   * Create and deliver a notification
   */
  async createNotification(
    userId: string,
    type: string,
    title: string,
    message: string,
    data?: any,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ) {
    console.log(`🔔 NotificationService: Creating ${type} notification for user ${userId}`);
    
    try {
      // Create the notification
      const notification = await storage.createNotification({
        userId,
        type,
        title,
        message,
        data: data ? JSON.stringify(data) : null,
        priority,
      });

      // Get user's notification preferences
      const preferences = await storage.getUserNotificationPreferences(userId);
      const typePrefs = preferences.find(p => p.type === type) || {
        inAppEnabled: true,
        emailEnabled: true,
        smsEnabled: false,
        frequency: 'immediate'
      };

      console.log(`📋 User preferences for ${type}:`, typePrefs);

      // Create delivery records based on preferences
      const deliveries = [];

      // In-app notification (always created)
      if (typePrefs.inAppEnabled !== false) {
        const inAppDelivery = await storage.createNotificationDelivery({
          notificationId: notification.id,
          deliveryMethod: 'in_app',
          recipient: userId,
          status: 'delivered',
        });
        deliveries.push(inAppDelivery);
      }

      // Email notification - PHASE 2: ACTUAL EMAIL SENDING
      if (typePrefs.emailEnabled) {
        try {
          // Get user's actual email address
          const user = await storage.getUser(userId);
          if (user && user.email) {
            console.log(`📧 Sending email notification to ${user.email}`);
            
            // Log email attempt to audit log
            await NotificationService.logEmailEvent(userId, 'email_notification_attempted', {
              notificationId: notification.id,
              emailAddress: user.email,
              notificationType: type,
              subject: title,
              recipient: user.email,
              deliveryMethod: 'email'
            });
            
            // Create delivery record first
            const emailDelivery = await storage.createNotificationDelivery({
              notificationId: notification.id,
              deliveryMethod: 'email',
              recipient: user.email,
              status: 'pending',
            });
            
            // Send the actual email based on notification type
            let emailResult;
            const userName = user.firstName || 'User';
            
            switch (type) {
              case 'rfq_uploaded':
                emailResult = await emailService.sendRfqUploadedEmail(user.email, userName, data);
                break;
              case 'bid_accepted':
                emailResult = await emailService.sendBidAcceptedEmail(user.email, userName, data);
                break;
              case 'bid_submitted':
                emailResult = await emailService.sendBidSubmittedEmail(user.email, userName, data);
                break;
              default:
                // Generic notification email
                emailResult = await emailService.sendNotificationEmail(
                  user.email,
                  userName,
                  title,
                  emailService.generateEmailTemplate(title, message, undefined, data)
                );
            }
            
            // Update delivery status based on email result
            if (emailResult.success) {
              await storage.updateDeliveryStatus(emailDelivery.id, 'delivered', emailResult.result);
              
              // Log successful email delivery to audit log
              await NotificationService.logEmailEvent(userId, 'email_notification_sent', {
                notificationId: notification.id,
                emailAddress: user.email,
                notificationType: type,
                subject: title,
                messageId: emailResult.messageId,
                deliveryMethod: 'email',
                providerResponse: emailResult.result,
                status: 'delivered'
              });
              
              console.log(`✅ Email delivered successfully to ${user.email}`);
            } else {
              await storage.updateDeliveryStatus(emailDelivery.id, 'failed', emailResult.error);
              
              // Log failed email delivery to audit log
              await NotificationService.logEmailEvent(userId, 'email_notification_failed', {
                notificationId: notification.id,
                emailAddress: user.email,
                notificationType: type,
                subject: title,
                deliveryMethod: 'email',
                errorMessage: emailResult.error,
                status: 'failed'
              });
              
              console.log(`❌ Email delivery failed to ${user.email}:`, emailResult.error);
            }
            
            deliveries.push(emailDelivery);
          } else {
            console.log(`⚠️ No email address found for user ${userId}, skipping email notification`);
          }
        } catch (emailError) {
          console.error(`❌ Email notification error for user ${userId}:`, emailError);
        }
      }

      console.log(`✅ Notification created with ${deliveries.length} delivery methods`);
      return { notification, deliveries };

    } catch (error) {
      console.error('❌ Failed to create notification:', error);
      throw error;
    }
  }

  /**
   * Convenience method for logging email events from other parts of the system
   */
  static async logEmailAuditEvent(
    userId: string,
    eventType: 'email_notification_attempted' | 'email_notification_sent' | 'email_notification_failed' | 'email_test_attempted' | 'email_test_sent' | 'email_test_failed',
    emailData: {
      emailAddress: string;
      subject: string;
      notificationType?: string;
      messageId?: string;
      errorMessage?: string;
      [key: string]: any;
    },
    request?: any
  ) {
    return await this.logEmailEvent(userId, eventType, emailData, request);
  }

  /**
   * Notification types for different business events
   */
  static NotificationTypes = {
    // RFQ Events
    RFQ_UPLOADED: 'rfq_uploaded',
    RFQ_DISTRIBUTED: 'rfq_distributed', 
    RFQ_CLOSED: 'rfq_closed',
    
    // Bid Events
    BID_SUBMITTED: 'bid_submitted',
    BID_ACCEPTED: 'bid_accepted',
    BID_REJECTED: 'bid_rejected',
    BID_REQUEST_INFO: 'bid_request_info',
    
    // System Events
    SYSTEM_MAINTENANCE: 'system_maintenance',
    ACCOUNT_UPDATE: 'account_update',
  };

  /**
   * Pre-configured notification templates for common events
   */
  async notifyRfqUploaded(userId: string, rfqData: { projectName: string; rfqId: string }) {
    return this.createNotification(
      userId,
      NotificationService.NotificationTypes.RFQ_UPLOADED,
      'RFQ Successfully Uploaded',
      `Your RFQ "${rfqData.projectName}" has been uploaded and is ready for distribution.`,
      rfqData,
      'medium'
    );
  }

  async notifyBidAccepted(userId: string, bidData: { projectName: string; bidId: string; amount: string }) {
    return this.createNotification(
      userId,
      NotificationService.NotificationTypes.BID_ACCEPTED,
      'Congratulations! Your Bid Was Accepted',
      `Your bid of ${bidData.amount} for "${bidData.projectName}" has been accepted.`,
      bidData,
      'high'
    );
  }

  async notifyBidSubmitted(userId: string, bidData: { projectName: string; bidId: string; contractorName: string }) {
    return this.createNotification(
      userId,
      NotificationService.NotificationTypes.BID_SUBMITTED,
      'New Bid Received',
      `${bidData.contractorName} has submitted a bid for "${bidData.projectName}".`,
      bidData,
      'medium'
    );
  }

  /**
   * Get user's unread notifications
   */
  async getUnreadNotifications(userId: string) {
    const notifications = await storage.getNotifications(userId);
    return notifications.filter(n => !n.readAt);
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string) {
    return storage.markNotificationRead(notificationId, userId);
  }

  /**
   * Update user notification preferences
   */
  async updatePreferences(
    userId: string, 
    type: string, 
    preferences: { 
      emailEnabled?: boolean; 
      inAppEnabled?: boolean; 
      smsEnabled?: boolean;
      frequency?: 'immediate' | 'daily' | 'weekly';
    }
  ) {
    console.log(`⚙️ Updating preferences for user ${userId}, type ${type}`);
    return storage.updateNotificationPreferences(userId, type, preferences);
  }

  /**
   * PHASE 2: Enhanced notification methods with email integration
   */

  // Static convenience methods for business workflow integration
  static async createRfqUploadNotification(data: {
    userId: string;
    rfqId: string;
    projectName: string;
    projectLocation?: string;
    dueDate?: Date;
    fileCount?: number;
  }) {
    return notificationService.createNotification(
      data.userId,
      NotificationService.NotificationTypes.RFQ_UPLOADED,
      'RFQ Successfully Uploaded',
      `Your RFQ "${data.projectName}" has been uploaded with ${data.fileCount || 0} files and is ready for distribution.`,
      {
        rfqId: data.rfqId,
        projectName: data.projectName,
        projectLocation: data.projectLocation,
        dueDate: data.dueDate,
        fileCount: data.fileCount
      },
      'medium'
    );
  }

  static async createBidSubmissionNotification(data: {
    rfqOwnerId: string;
    contractorId: string;
    contractorName: string;
    bidId: string;
    rfqId: string;
    projectName: string;
    bidAmount?: number;
    submissionDate: Date;
  }) {
    return notificationService.createNotification(
      data.rfqOwnerId,
      NotificationService.NotificationTypes.BID_SUBMITTED,
      'New Bid Received',
      `${data.contractorName} has submitted a bid${data.bidAmount ? ` of $${data.bidAmount.toLocaleString()}` : ''} for "${data.projectName}".`,
      {
        bidId: data.bidId,
        rfqId: data.rfqId,
        contractorId: data.contractorId,
        contractorName: data.contractorName,
        projectName: data.projectName,
        bidAmount: data.bidAmount,
        submissionDate: data.submissionDate
      },
      'high'
    );
  }

  static async createBidActionNotification(data: {
    contractorUserId: string;
    rfqOwnerId: string;
    bidId: string;
    rfqId: string;
    projectName: string;
    action: 'accepted' | 'rejected';
    notes?: string;
    actionDate: Date;
  }) {
    const isAccepted = data.action === 'accepted';
    return notificationService.createNotification(
      data.contractorUserId,
      isAccepted ? NotificationService.NotificationTypes.BID_ACCEPTED : NotificationService.NotificationTypes.BID_REJECTED,
      isAccepted ? 'Congratulations! Your Bid Was Accepted' : 'Bid Status Update',
      `Your bid for "${data.projectName}" has been ${data.action}.${data.notes ? ` Note: ${data.notes}` : ''}`,
      {
        bidId: data.bidId,
        rfqId: data.rfqId,
        projectName: data.projectName,
        action: data.action,
        notes: data.notes,
        actionDate: data.actionDate
      },
      isAccepted ? 'high' : 'medium'
    );
  }
  
  async sendTestEmailNotification(userId: string, recipientEmail?: string) {
    console.log(`🧪 Sending test email notification for user ${userId}`);
    
    try {
      const user = await storage.getUser(userId);
      const email = recipientEmail || user?.email;
      const name = user?.firstName || 'Test User';
      
      if (!email) {
        throw new Error('No email address available for test');
      }
      
      const result = await emailService.sendTestEmail(email, name);
      
      // Create notification record
      const notification = await this.createNotification(
        userId,
        'system_test',
        'Email Test Notification',
        'This is a test to verify email notifications are working correctly.',
        { testType: 'email_integration', timestamp: new Date() },
        'medium'
      );
      
      return { success: result.success, notification, email };
      
    } catch (error) {
      console.error('❌ Test email notification failed:', error);
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Upload, FileText, Loader2, CheckCircle, AlertCircle, User, Calculator, Package, DollarSign } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { BidLineItemForm } from "./BidLineItemForm";
import { BidScopeDefinition } from "./BidScopeDefinition";
import { UploadProgressIndicator } from "./UploadProgressIndicator";

interface BidSubmissionFormProps {
  rfqId: string;
  onSuccess?: () => void;
}

// Enhanced schema with structured data
const bidLineItemSchema = z.object({
  costCode: z.string().min(1, "Cost code is required"),
  description: z.string().min(1, "Description is required"),
  quantity: z.number().min(0, "Quantity must be positive"),
  unitPrice: z.number().min(0, "Unit price must be positive"),
  totalPrice: z.number().min(0, "Total price must be positive"),
  unitOfMeasure: z.string(),
  category: z.string(),
});

const scopeItemSchema = z.object({
  type: z.enum(['included', 'excluded']),
  description: z.string().min(1, "Description is required"),
  category: z.string(),
});

const bidSubmissionSchema = z.object({
  // Contact Information - optional for document-only submissions
  bidContactName: z.string().optional(),
  bidContactEmail: z.string().email("Valid email is required").or(z.string().length(0)),
  bidContactPhone: z.string().optional(),
  
  // Pricing Structure
  lineItems: z.array(bidLineItemSchema).min(0),
  totalBidAmount: z.number().min(0, "Total bid amount must be positive"),
  
  // Scope Definition
  includedItems: z.array(scopeItemSchema).min(0),
  excludedItems: z.array(scopeItemSchema).min(0),
  
  // Timeline and Additional Information - optional for document-only submissions
  timeline: z.string().optional(),
  notes: z.string().optional(),
});

type BidSubmissionData = z.infer<typeof bidSubmissionSchema>;

export function BidSubmissionForm({ rfqId, onSuccess }: BidSubmissionFormProps) {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [lineItemsTotal, setLineItemsTotal] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadSessionId, setUploadSessionId] = useState<string | null>(null);
  const [showProgress, setShowProgress] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<BidSubmissionData>({
    resolver: zodResolver(bidSubmissionSchema),
    defaultValues: {
      bidContactName: "",
      bidContactEmail: "",
      bidContactPhone: "",
      lineItems: [],
      totalBidAmount: 0,
      includedItems: [],
      excludedItems: [],
      timeline: "",
      notes: "",
    },
  });

  // Create progress tracking session
  const createSessionMutation = useMutation({
    mutationFn: async (): Promise<{ sessionId: string }> => {
      const response = await apiRequest('POST', '/api/upload/start-session');
      return { sessionId: response.sessionId || `session-${Date.now()}` };
    },
  });

  // Calculate total bid amount when line items change
  const calculateTotalBidAmount = (lineItemsTotal: number) => {
    form.setValue('totalBidAmount', lineItemsTotal);
    return lineItemsTotal;
  };

  // Total bid amount equals line items total
  const totalBidAmount = calculateTotalBidAmount(lineItemsTotal);

  const submitBidMutation = useMutation({
    mutationFn: async (data: BidSubmissionData & { files?: FileList; sessionId?: string }) => {
      const formData = new FormData();
      
      // Add progress tracking session ID if available
      if (data.sessionId) {
        formData.append('sessionId', data.sessionId);
      }
      
      // Add structured bid data (only if provided)
      if (data.bidContactName) formData.append('bidContactName', data.bidContactName);
      if (data.bidContactEmail) formData.append('bidContactEmail', data.bidContactEmail);
      if (data.bidContactPhone) formData.append('bidContactPhone', data.bidContactPhone);
      
      formData.append('totalBidAmount', data.totalBidAmount.toString());
      if (data.timeline) formData.append('timeline', data.timeline);
      if (data.notes) formData.append('notes', data.notes);
      
      formData.append('lineItemsTotal', lineItemsTotal.toString());
      
      // Add line items as JSON
      if (data.lineItems.length > 0) {
        formData.append('lineItems', JSON.stringify(data.lineItems));
      }
      
      // Add scope definitions as JSON
      if (data.includedItems.length > 0) {
        formData.append('includedItems', JSON.stringify(data.includedItems));
      }
      if (data.excludedItems.length > 0) {
        formData.append('excludedItems', JSON.stringify(data.excludedItems));
      }
      
      // Add files if selected
      if (data.files) {
        Array.from(data.files).forEach((file) => {
          formData.append('bidDocuments', file);
        });
      }

      return apiRequest('POST', `/api/rfqs/${rfqId}/bids`, formData);
    },
    onSuccess: () => {
      setIsProcessing(false);
      setShowProgress(false);
      setUploadSessionId(null);
      queryClient.invalidateQueries({ queryKey: ['/api/rfqs', rfqId, 'bids'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contractors/bids'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contractors/rfqs'] });
      toast({
        title: "Bid Submitted Successfully",
        description: "Your bid package has been processed with AI analysis and submitted to the general contractor.",
      });
      form.reset();
      setSelectedFiles(null);
      onSuccess?.();
    },
    onError: (error: any) => {
      setIsProcessing(false);
      setShowProgress(false);
      setUploadSessionId(null);
      toast({
        title: "Bid Submission Failed", 
        description: error.message || "Failed to submit bid. Please check your files and try again.",
        variant: "destructive",
      });
    },
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      setSelectedFiles(e.dataTransfer.files);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFiles(e.target.files);
    }
  };

  const onSubmit = async (data: BidSubmissionData) => {
    // Validate that we have either structured data or documents
    const hasStructuredData = data.lineItems.length > 0 || data.totalBidAmount > 0;
    const hasDocuments = selectedFiles && selectedFiles.length > 0;
    
    if (!hasStructuredData && !hasDocuments) {
      toast({
        title: "Bid Data Required",
        description: "Please either enter structured bid data or upload bid documents.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    // If we have files, create progress tracking session
    if (hasDocuments && selectedFiles) {
      try {
        const sessionData = await createSessionMutation.mutateAsync();
        setUploadSessionId(sessionData.sessionId);
        setShowProgress(true);
        
        submitBidMutation.mutate({ 
          ...data, 
          files: selectedFiles, 
          sessionId: sessionData.sessionId 
        });
      } catch (error) {
        console.error('Failed to create session:', error);
        // Fallback to regular upload without progress
        submitBidMutation.mutate({ ...data, files: selectedFiles });
      }
    } else {
      // No files - submit structured data only
      submitBidMutation.mutate({ ...data });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Submit Your Bid
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-primary">
              ${totalBidAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
            <div className="text-sm text-muted-foreground">Total Bid Amount</div>
          </div>
        </CardTitle>
        <CardDescription>
          Provide structured bid information for accurate comparison and analysis.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Step-by-step Instructions */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">📋 How to Submit Your Bid</h3>
          <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
            Complete each section in order for the most accurate bid submission:
          </p>
          <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li><strong>1. Contact Info</strong> - Enter your primary contact details for this project</li>
            <li><strong>2. Pricing</strong> - Set your total bid amount and markup percentage</li>
            <li><strong>3. Line Items</strong> - Add detailed cost breakdown by trade category</li>  
            <li><strong>4. Scope</strong> - Specify what's included and excluded in your bid</li>
            <li><strong>5. Documents</strong> - Upload your bid package for AI analysis</li>
          </ol>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="contact" className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="contact" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Contact
                </TabsTrigger>
                <TabsTrigger value="pricing" className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Pricing
                </TabsTrigger>
                <TabsTrigger value="lineItems" className="flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  Line Items
                </TabsTrigger>
                <TabsTrigger value="scope" className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Scope
                </TabsTrigger>
                <TabsTrigger value="documents" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Documents
                </TabsTrigger>
              </TabsList>

              {/* Contact Information Tab */}
              <TabsContent value="contact" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Contact Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="bidContactName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Name *</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Primary contact for this bid" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="bidContactEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Email *</FormLabel>
                          <FormControl>
                            <Input {...field} type="email" placeholder="<EMAIL>" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="bidContactPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Phone (Optional)</FormLabel>
                          <FormControl>
                            <Input {...field} type="tel" placeholder="(*************" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="timeline"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Project Timeline *</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="e.g., 4 weeks, 60 days" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Pricing Tab */}
              <TabsContent value="pricing" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5" />
                      Pricing Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="space-y-2">
                        <FormLabel>Line Items Total</FormLabel>
                        <div className="text-2xl font-bold text-muted-foreground">
                          ${lineItemsTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Calculated from line items
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <FormLabel>Total Bid Amount</FormLabel>
                        <div className="text-2xl font-bold text-primary">
                          ${totalBidAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Total from line items
                        </p>
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Additional Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="Add any additional information about your pricing..."
                              rows={3}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Line Items Tab */}
              <TabsContent value="lineItems" className="space-y-4">
                <BidLineItemForm 
                  name="lineItems" 
                  onTotalChange={(total) => {
                    setLineItemsTotal(total);
                    calculateTotalBidAmount(total);
                  }}
                />
              </TabsContent>

              {/* Scope Definition Tab */}
              <TabsContent value="scope" className="space-y-4">
                <BidScopeDefinition 
                  includedName="includedItems"
                  excludedName="excludedItems"
                />
              </TabsContent>

              {/* Documents Tab */}
              <TabsContent value="documents" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Supporting Documents
                    </CardTitle>
                    <CardDescription>
                      Upload supporting documents (optional with structured data entry)
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div
                      className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                        dragActive 
                          ? "border-primary bg-primary/5" 
                          : "border-muted-foreground/25 hover:border-muted-foreground/50"
                      }`}
                      onDragEnter={handleDrag}
                      onDragLeave={handleDrag}
                      onDragOver={handleDrag}
                      onDrop={handleDrop}
                    >
                      <input
                        type="file"
                        multiple
                        accept=".pdf,.csv,.txt"
                        onChange={handleFileSelect}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                        <p className="text-lg font-medium mb-2">
                          Drop your documents here, or click to browse
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Supports PDF, TXT, and CSV files up to 250MB
                        </p>
                        {selectedFiles && Array.from(selectedFiles).some(file => file.size > 100 * 1024 * 1024) && (
                          <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg dark:bg-amber-950/20 dark:border-amber-800">
                            <div className="flex items-start gap-2">
                              <div className="flex-shrink-0 mt-0.5">
                                <svg className="h-4 w-4 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="text-sm">
                                <p className="font-medium text-amber-800 dark:text-amber-200">Large File Processing</p>
                                <p className="text-amber-700 dark:text-amber-300">Files over 100MB may take 5-15 minutes to upload and process. The upload is resumable if interrupted.</p>
                              </div>
                            </div>
                          </div>
                        )}
                      </label>
                    </div>

                    {/* Selected Files Display */}
                    {selectedFiles && selectedFiles.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Selected Files:</p>
                        {Array.from(selectedFiles).map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4" />
                              <span className="text-sm font-medium">{file.name}</span>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {formatFileSize(file.size)}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* AI Processing Info */}
                    <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                      <div className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-900 dark:text-blue-100">
                            AI Document Processing
                          </h4>
                          <p className="text-sm text-blue-700 dark:text-blue-200 mt-1">
                            Documents will be processed to extract additional details and verify structured data accuracy.
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Progress Indicator */}
            {showProgress && uploadSessionId && selectedFiles && (
              <div className="space-y-4">
                <UploadProgressIndicator
                  sessionId={uploadSessionId}
                  files={Array.from(selectedFiles)}
                  onComplete={() => {
                    setShowProgress(false);
                    setIsProcessing(false);
                  }}
                  onError={(error) => {
                    setShowProgress(false);
                    setIsProcessing(false);
                    toast({
                      title: "Upload Failed",
                      description: error,
                      variant: "destructive",
                    });
                  }}
                />
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t">
              <Button 
                type="submit" 
                size="lg"
                className="w-full md:w-auto px-8" 
                disabled={submitBidMutation.isPending || isProcessing}
              >
                {submitBidMutation.isPending || isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {showProgress ? "Processing & Submitting Bid..." : "Submitting Bid..."}
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Submit Bid - ${totalBidAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
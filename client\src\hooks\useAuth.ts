import { useQuery } from "@tanstack/react-query";
import { getQueryFn } from "@/lib/queryClient";

export function useAuth() {
  console.log("🔐 useAuth: Hook called");
  
  const { data: user, isLoading, error } = useQuery({
    queryKey: ["/api/auth/user"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  console.log("🔐 useAuth: State", { 
    hasUser: !!user, 
    isLoading, 
    hasError: !!error,
    errorMessage: error?.message 
  });

  // If there's an error that's not a 401, treat as not authenticated
  // This handles cases where the query might fail for other reasons
  const isAuthenticated = !!user;

  return {
    user,
    isLoading,
    isAuthenticated,
  };
}

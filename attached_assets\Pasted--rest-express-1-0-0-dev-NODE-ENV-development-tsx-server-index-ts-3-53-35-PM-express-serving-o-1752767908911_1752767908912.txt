
> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

3:53:35 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 9 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
3:53:40 PM [express] GET /api/auth/user 401 in 2ms :: {"message":"Unauthorized"}
3:53:51 PM [express] GET /api/auth/user 304 in 483ms :: {"id":"34020394","email":"roy.gatling@snapz.…
3:53:52 PM [express] GET /api/dashboard/stats 304 in 318ms :: {"activeRfqs":"1","totalBids":"3","avg…
3:53:52 PM [express] GET /api/rfqs 304 in 586ms :: [{"id":"a3f3534a-8d65-46dd-be35-6fc5ec8920f7","cr…
3:56:36 PM [vite] hmr update /src/pages/HelpSupport.tsx, /src/index.css?v=mug25zEwzIByyrdZG8jbR
3:56:39 PM [vite] hmr update /src/pages/HelpSupport.tsx, /src/index.css?v=mug25zEwzIByyrdZG8jbR (x2)
3:58:11 PM [vite] hmr update /src/pages/HelpSupport.tsx, /src/index.css?v=mug25zEwzIByyrdZG8jbR (x3)
3:58:14 PM [vite] Internal server error: /home/<USER>/workspace/client/src/pages/HelpSupport.tsx: Unterminated JSX contents. (229:13)

  227 |           </div>
  228 |         </CardContent>
> 229 |       </Card>
      |              ^
  230 |
  231 |
  232 |
  Plugin: vite:react-babel
  File: /home/<USER>/workspace/client/src/pages/HelpSupport.tsx:229:13
  227|            </div>
  228|          </CardContent>
  229|        </Card>
     |               ^
  230|  
  231|  
      at toParseError (/home/<USER>/workspace/node_modules/@babel/parser/src/parse-error.ts:95:45)
      at raise (/home/<USER>/workspace/node_modules/@babel/parser/src/tokenizer/index.ts:1497:19)
      at jsxReadToken (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:104:22)
      at getTokenFromCode (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:596:14)
      at getTokenFromCode (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/typescript/index.ts:3975:13)
      at nextToken (/home/<USER>/workspace/node_modules/@babel/parser/src/tokenizer/index.ts:274:10)
      at next (/home/<USER>/workspace/node_modules/@babel/parser/src/tokenizer/index.ts:121:10)
      at eat (/home/<USER>/workspace/node_modules/@babel/parser/src/tokenizer/index.ts:126:12)
      at expect (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/util.ts:156:15)
      at jsxParseClosingElementAt (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:460:12)
      at jsxParseElementAt (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:480:39)
      at jsxParseElementAt (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:483:34)
      at jsxParseElement (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:559:19)
      at parseExprAtom (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:573:21)
      at parseExprSubscripts (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:714:23)
      at parseUpdate (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:693:21)
      at parseMaybeUnary (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:655:23)
      at parseMaybeUnary (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/typescript/index.ts:3735:20)
      at parseMaybeUnaryOrPrivate (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:389:14)
      at parseExprOps (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:401:23)
      at parseMaybeConditional (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:356:23)
      at parseMaybeAssign (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:298:21)
      at fn (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/typescript/index.ts:3588:23)
      at tryParse (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/util.ts:174:20)
      at parseMaybeAssign (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/typescript/index.ts:3587:20)
      at callback (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:252:12)
      at allowInAnd (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:3117:12)
      at parseMaybeAssignAllowIn (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:251:17)
      at parseParenAndDistinguishExpression (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:1771:16)
      at parseExprAtom (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:1131:21)
      at parseExprAtom (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/jsx/index.ts:583:22)
      at parseExprSubscripts (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:714:23)
      at parseUpdate (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:693:21)
      at parseMaybeUnary (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:655:23)
      at parseMaybeUnary (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/typescript/index.ts:3735:20)
      at parseMaybeUnaryOrPrivate (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:389:14)
      at parseExprOps (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:401:23)
      at parseMaybeConditional (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:356:23)
      at parseMaybeAssign (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:298:21)
      at parseMaybeAssign (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/typescript/index.ts:3607:22)
      at parseExpressionBase (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:221:23)
      at callback (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:212:39)
      at allowInAnd (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:3112:16)
      at parseExpression (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/expression.ts:212:17)
      at parseReturnStatement (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/statement.ts:1091:28)
      at parseStatementContent (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/statement.ts:498:21)
      at parseStatementContent (/home/<USER>/workspace/node_modules/@babel/parser/src/plugins/typescript/index.ts:3056:20)
      at parseStatementLike (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/statement.ts:437:17)
      at parseStatementListItem (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/statement.ts:386:17)
      at parseBlockOrModuleBlockBody (/home/<USER>/workspace/node_modules/@babel/parser/src/parser/statement.ts:1420:16)
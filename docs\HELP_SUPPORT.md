
# Bidaible Help & Support

## Getting Started

### What is Bidaible?
Bidaible is a comprehensive platform designed to streamline the construction bidding process. It connects project owners with qualified contractors, making it easier to manage RFQs (Request for Quotes), submit bids, and track project progress with AI-powered document processing and analysis.

### Key Features
- **AI-Powered RFQ Management**: Create, manage, and track requests for quotes with intelligent document extraction
- **Contractor Network**: Access to a verified network of construction professionals across 29 trade categories
- **Advanced Bid Analysis**: AI-powered bid comparison with competitive scoring and risk assessment
- **✅ Comprehensive Bid Comparison**: Side-by-side bid analysis with detailed CSI cost code breakdowns
- **✅ Detailed CSV Export**: Export accepted bids with individual cost code line items for budget planning and ERP integration
- **Document Processing**: Automated extraction from PDF, TXT, and CSV files up to 50MB
- **Templates & Resources**: Professional bidding templates and cost code references
- **API Integration**: Comprehensive API with QuickBooks and Sage ERP connectivity

## Navigation Guide

### Dashboard
Your central hub showing:
- Active RFQs count with real-time updates
- Total bids received with status tracking
- Average bid amount calculations
- Recent activity timeline
- Quick access to AI bid analysis

### Available RFQs
Browse and search through open RFQs from project owners:
- Advanced filtering by trade category, location, and deadline
- Real-time updates on new opportunities
- Detailed project specifications and requirements
- Direct bid submission interface

### My RFQs
Comprehensive RFQ management for project owners:
- Create new RFQs with AI document processing
- View and manage submitted bids with analysis tools
- **✅ Side-by-Side Bid Comparison**: Compare multiple bids with detailed cost code breakdowns
- **✅ Export Accepted Bids**: Download detailed CSV with individual cost codes for budget planning
- Track document uploads and contractor responses
- Monitor RFQ status through complete lifecycle

### My Bids
Complete bid tracking and management:
- View bid status (pending, accepted, rejected)
- Edit draft bids before submission
- Access complete bid history and analytics
- Performance tracking and success metrics

### Contractors
Advanced contractor network management:
- Search across 29 specialized trade categories
- View detailed contractor profiles with verification status
- Manage favorite contractors for targeted invitations
- Track contractor performance and reliability

### Templates & Resources
Professional bidding resources:
- Downloadable bid response templates
- Master Cost Codes CSV with Procore integration
- Industry best practices and guidelines
- Cost categorization reference materials

## Comprehensive Bid Comparison & Export System

### How to Use Side-by-Side Bid Comparison
1. **Navigate to Your RFQ**: Go to "My RFQs" and select an RFQ with submitted bids
2. **Access Comprehensive Analysis**: Click on the "Comprehensive Bid Comparison" tab or view
3. **Review Bid Cards**: Compare multiple contractor bids side-by-side showing:
   - Contractor company information and contact details
   - Individual cost code line items (16.010, 16.015, etc.)
   - Detailed descriptions for each cost code
   - Quantities, unit prices, and line totals
   - Total bid amounts and categories
4. **Accept/Reject Bids**: Use the action buttons on each bid card to accept or reject proposals

### Detailed CSV Export Functionality
**Export Accepted Bids CSV Button**: 
- **Location**: Available in the Comprehensive Bid Comparison view when you have accepted bids
- **Purpose**: Download detailed cost code breakdown for budget planning and ERP integration
- **Format**: Individual rows for each cost code showing:
  - Contractor Company & Contact Name
  - Cost Code (16.010, 16.015, etc.)
  - Cost Description
  - Quantity & Unit Price
  - Line Total & Category
  - Total Bid Amount

**Export Use Cases**:
- Project budget planning with line-item detail
- ERP system integration (QuickBooks, Sage, etc.)
- Cost analysis and variance tracking
- Contractor performance comparison
- Financial reporting and audit trails

### Smart Export Logic
- **Dashboard CSV Export**: Automatically exports detailed cost breakdowns for accepted bids, summary data for all others
- **Individual Export**: Use the dedicated "Export Accepted Bids CSV" button for focused detailed exports
- **Date-Stamped Files**: All exports include date stamps for easy file management

## Role-Based Access Control

### User Roles
- **SuperUser**: Complete system access across all organizations
- **Organization Admin**: Full organization management with user administration (max 15 users)
- **Standard User**: General access for RFQ and bid management within organization

### Security Features
- Organization-scoped data access with complete tenant isolation
- Comprehensive audit logging for all role changes and access attempts
- Advanced API key management with scoped permissions
- Real-time security monitoring and threat detection

## Common Tasks

### Creating an RFQ with AI Processing
1. Click "Create RFQ" button in the sidebar
2. Fill in basic project information (name, location, timeline)
3. Upload project documents (PDF, TXT, CSV up to 50MB)
4. AI automatically extracts key details using Google Gemini
5. Review and edit extracted information for accuracy
6. Select contractors from favorites or broadcast to all qualified
7. Set submission deadline and publish RFQ

### Advanced Bid Analysis
1. Navigate to RFQ bid management dashboard
2. Access "AI Analysis" tab for comprehensive insights
3. Review executive summary with competitive landscape
4. Analyze scoring matrix with detailed reasoning
5. Examine risk factors and recommendations
6. Use market analysis for negotiation insights

### API Key Management
1. Navigate to Settings → API Keys
2. Click "Create New API Key" with custom name
3. Select permission level (read-only, upload-only, full-access)
4. Configure rate limiting (default: 100 requests/hour)
5. Copy key securely (masked display with visibility toggle)
6. Monitor usage statistics and audit trails

### Contractor Favorites Management
1. Browse contractor profiles by trade category
2. Click star icon to add to favorites list
3. Use favorites for targeted RFQ distribution
4. Track favorite contractor performance metrics
5. Manage favorites through dedicated interface

## API Integration

### Authentication
- JWT-based API keys with scoped permissions
- Secure key storage with SHA-256 hashing
- Rate limiting with configurable per-key limits
- Comprehensive usage tracking and analytics

### Core Endpoints
- **RFQ Management**: Create, read, update RFQs with document processing
- **Bid Submission**: Submit bids with validation and file uploads
- **AI Analysis**: Access bid analysis with competitive scoring
- **Data Export**: CSV/JSON export with date filtering
- **ERP Integration**: QuickBooks and Sage ERP synchronization

### Integration Examples
- JavaScript fetch with error handling
- Python requests library implementation
- curl commands with authentication
- Real-time webhook notifications

## Troubleshooting

### Common Issues

**Document Processing Failures**
- Ensure files are PDF, TXT, or CSV format
- Check file size is under 50MB limit
- Verify file is not corrupted or password-protected
- Contact support for complex document structures

**API Authentication Issues**
- Verify API key format and permissions
- Check rate limiting status in headers
- Ensure proper Authorization header format
- Review audit logs for failed attempts

**RFQ Visibility Problems**
- Confirm RFQ is published and active
- Check organization access permissions
- Verify deadline hasn't passed
- Review trade category and location filters

**Bid Submission Errors**
- Validate all required fields are completed
- Check file upload size and format requirements
- Ensure stable internet connection
- Verify bid deadline hasn't expired

### Performance Optimization
- Use proper file compression for uploads
- Implement client-side caching for repeated API calls
- Monitor rate limiting headers to avoid throttling
- Use pagination for large data sets

## Security Best Practices

### API Security
- Store API keys securely, never in client-side code
- Use environment variables for production deployments
- Implement proper error handling to avoid information leakage
- Monitor API key usage for suspicious activity
- Rotate keys regularly following security protocols

### Account Security
- Enable strong authentication methods
- Monitor login history and unusual access patterns
- Report suspicious activity immediately
- Use organization-scoped access controls
- Regular security audits and compliance reviews

## Support Channels

### Contact Information
- **Email**: <EMAIL> (24-hour response)
- **Phone**: 1-800-BIDAIBLE (business hours)
- **Live Chat**: 9 AM - 6 PM EST (weekdays)
- **Documentation**: Comprehensive online help center

### Response Times
- **Critical Issues**: Within 2 hours
- **API Integration Support**: Within 4 hours
- **General Support**: Within 24 hours
- **Feature Requests**: Within 48 hours

### What to Include in Support Requests
1. Account email and organization details
2. Detailed description with steps to reproduce
3. Error messages and relevant logs
4. Screenshots or screen recordings
5. Browser/system information and API details

## Training Resources

### Documentation
- Interactive API documentation with live examples
- Video tutorials for key workflows
- Best practices guides for construction bidding
- Integration examples and code samples

### Webinars and Training
- Monthly "Bidaible Best Practices" sessions
- Industry-specific training programs
- API integration workshops
- New feature announcements and demos

## Updates & Announcements

### Recent Enhancements
- **AI Bid Analysis**: Deep analytical consideration with Groq integration
- **Master Cost Codes**: Comprehensive cost reference with Procore mapping
- **Enhanced Security**: Advanced role-based access control with audit trails
- **API Expansion**: Complete programmatic access with ERP integrations
- **Material Intelligence**: MatIQ integration for market forecasting

### Version History
- Regular feature updates with backward compatibility
- Security patches and performance improvements
- API versioning with migration guides
- User interface enhancements and accessibility improvements

---

*Last updated: January 2025*
*Version: 2.0*

For immediate assistance, please contact our support <NAME_EMAIL>


# Backend Documentation

## Overview

The Bidaible backend is built with Node.js, Express, TypeScript, and PostgreSQL to provide a robust, scalable API for construction bidding management with AI-powered document processing and analysis.

## Architecture

### Technology Stack
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js with custom middleware
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Replit Auth with JWT API keys
- **AI Services**: Google Gemini API, Groq API for analysis
- **File Storage**: Replit Object Storage
- **Caching**: In-memory cache with TTL and LRU eviction

### Project Structure
```
server/
├── services/           # Business logic services
├── middleware/         # Express middleware
├── routes/            # API route handlers
├── db.ts              # Database configuration
├── storage.ts         # Data access layer
├── index.ts           # Application entry point
└── routes.ts          # Main routing configuration
```

## Core Services

### AI Services

#### Primary AI Service (aiService.ts)
```typescript
// File: server/services/aiService.ts
export async function extractRFQData(extractedText: string): Promise<ExtractedData> {
  const prompt = `Extract key information from this RFQ document...`;
  
  try {
    const response = await geminiClient.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }]
    });
    
    return parseAIResponse(response);
  } catch (error) {
    console.error('AI extraction failed:', error);
    throw new Error('Failed to extract RFQ data');
  }
}
```

**Features**:
- Document data extraction
- Project detail recognition
- Requirements parsing
- Deadline identification
- Fallback error handling

#### Bid Analysis Service (bidAnalysisService.ts)
```typescript
// File: server/services/bidAnalysisService.ts
export async function generateBidAnalysis(rfqId: string): Promise<BidAnalysis> {
  const bids = await storage.getBidsByRfqWithContractors(rfqId);
  
  const analysisPrompt = `Analyze these construction bids and provide:
    1. Executive summary
    2. Competitive scoring (1-100)
    3. Risk assessment
    4. Market analysis
    5. Recommendations`;
  
  const response = await groqClient.chat.completions.create({
    model: "moonshotai/kimi-k2-instruct",
    messages: [{ role: "user", content: analysisPrompt }]
  });
  
  return parseAnalysisResponse(response);
}
```

**Features**:
- Groq AI integration for rapid analysis (3-second generation)
- Executive summary creation
- Competitive scoring and ranking
- Risk assessment with categorization
- Market analysis with recommendations

#### Master Summary Service (masterSummaryService.ts)
```typescript
// File: server/services/masterSummaryService.ts
export async function generateMasterSummary(rfqId: string): Promise<{
  masterSummary: string;
  conflicts: ConflictFlag[];
}> {
  const documents = await storage.getRfqDocuments(rfqId);
  const conflicts = detectConflicts(documents);
  
  const summaryPrompt = `Create a master summary consolidating all documents...`;
  const summary = await generateConsolidatedSummary(documents, conflicts);
  
  return { masterSummary: summary, conflicts };
}
```

**Features**:
- Multi-document consolidation
- Conflict detection (dates, amounts, requirements)
- Severity assessment (low, medium, high)
- HTML-formatted output

### Authentication Services

#### API Key Service (apiKeyService.ts)
```typescript
// File: server/services/apiKeyService.ts
export async function generateApiKey(
  userId: string,
  name: string,
  permissions: ApiKeyPermissions,
  rateLimit: number
): Promise<ApiKeyResponse> {
  const keyId = generateUniqueId();
  const secretKey = generateSecureKey();
  
  const token = jwt.sign(
    { userId, keyId, permissions },
    process.env.JWT_SECRET!,
    { expiresIn: '1y' }
  );
  
  await storage.createApiKey({
    id: keyId,
    userId,
    name,
    hashedKey: await hashKey(secretKey),
    permissions,
    rateLimit
  });
  
  return {
    id: keyId,
    key: `bda_${token}`,
    name,
    permissions,
    rateLimit
  };
}
```

**Features**:
- JWT-based token generation
- Scoped permissions (read-only, upload-only, full-access)
- Rate limiting configuration
- Secure key hashing
- Usage tracking

#### Authentication Middleware (apiAuth.ts)
```typescript
// File: server/middleware/apiAuth.ts
export const authenticateApiKey = async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  if (authHeader?.startsWith('Bearer bda_')) {
    const token = authHeader.slice(11); // Remove 'Bearer bda_'
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
      const apiKey = await storage.getApiKey(decoded.keyId);
      
      if (!apiKey || !apiKey.isActive) {
        return res.status(401).json({ error: 'Invalid API key' });
      }
      
      // Rate limiting check
      const isAllowed = await checkRateLimit(apiKey.id, apiKey.rateLimit);
      if (!isAllowed) {
        return res.status(429).json({ error: 'Rate limit exceeded' });
      }
      
      req.user = { ...decoded, apiKey };
      next();
    } catch (error) {
      return res.status(401).json({ error: 'Invalid token' });
    }
  } else {
    next(); // Fall through to session auth
  }
};
```

### File Processing Services

#### Object Storage Service (objectStorageService.ts)
```typescript
// File: server/services/objectStorageService.ts
export async function uploadFile(file: Express.Multer.File): Promise<string> {
  const fileName = `${Date.now()}-${file.originalname}`;
  const uploadUrl = await getUploadUrl(fileName);
  
  const response = await fetch(uploadUrl, {
    method: 'PUT',
    body: file.buffer,
    headers: {
      'Content-Type': file.mimetype,
      'Content-Length': file.size.toString()
    }
  });
  
  if (!response.ok) {
    throw new Error('Failed to upload file');
  }
  
  return fileName;
}
```

**Features**:
- Replit Object Storage integration
- Secure file upload with validation
- Automatic file naming
- Error handling and retry logic

#### File Processing Service (fileProcessingService.ts)
```typescript
// File: server/services/fileProcessingService.ts
export async function processDocument(file: Express.Multer.File): Promise<ProcessedDocument> {
  let extractedText = '';
  
  switch (file.mimetype) {
    case 'application/pdf':
      extractedText = await extractPDFText(file.buffer);
      break;
    case 'text/plain':
      extractedText = file.buffer.toString('utf-8');
      break;
    case 'text/csv':
      extractedText = await parseCSV(file.buffer);
      break;
    default:
      throw new Error(`Unsupported file type: ${file.mimetype}`);
  }
  
  const aiData = await extractRFQData(extractedText);
  
  return {
    extractedText,
    aiData,
    fileInfo: {
      originalName: file.originalname,
      size: file.size,
      mimeType: file.mimetype
    }
  };
}
```

### Caching Service

#### Cache Service (cacheService.ts)
```typescript
// File: server/services/cacheService.ts
class CacheService {
  private cache = new Map<string, CacheItem>();
  private maxSize = 1000;
  private defaultTTL = 300000; // 5 minutes

  set(key: string, value: any, ttl?: number): void {
    const expiresAt = Date.now() + (ttl || this.defaultTTL);
    
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }
    
    this.cache.set(key, {
      value,
      expiresAt,
      lastAccessed: Date.now()
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item || item.expiresAt < Date.now()) {
      this.cache.delete(key);
      return null;
    }
    
    item.lastAccessed = Date.now();
    return item.value;
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const [key] of this.cache) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }
}
```

**Features**:
- In-memory caching with TTL
- LRU eviction policy
- Pattern-based invalidation
- Performance monitoring

## Database Layer

### Schema Definition (shared/schema.ts)
```typescript
// File: shared/schema.ts
export const rfqs = pgTable('rfqs', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectName: varchar('project_name', { length: 255 }).notNull(),
  projectDescription: text('project_description'),
  projectLocation: varchar('project_location', { length: 255 }),
  dueDate: timestamp('due_date'),
  status: varchar('status', { length: 50 }).default('draft'),
  createdBy: varchar('created_by', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  extractedData: jsonb('extracted_data'),
  aiSummary: text('ai_summary'),
  masterSummary: text('master_summary'),
  conflictFlags: jsonb('conflict_flags'),
  summaryGeneratedAt: timestamp('summary_generated_at')
});
```

### Data Access Layer (storage.ts)
```typescript
// File: server/storage.ts
export class Storage {
  async createRfq(rfqData: CreateRfqData): Promise<Rfq> {
    const [rfq] = await db.insert(rfqs).values(rfqData).returning();
    return rfq;
  }

  async getRfq(id: string): Promise<Rfq | null> {
    const cacheKey = `rfq:${id}`;
    const cached = cache.get(cacheKey);
    if (cached) return cached;

    const [rfq] = await db.select().from(rfqs).where(eq(rfqs.id, id));
    if (rfq) {
      cache.set(cacheKey, rfq);
    }
    return rfq || null;
  }

  async updateRfq(id: string, updates: Partial<Rfq>): Promise<void> {
    await db.update(rfqs).set(updates).where(eq(rfqs.id, id));
    cache.invalidatePattern(`rfq:${id}`);
  }
}
```

**Features**:
- Drizzle ORM for type-safe database operations
- Caching integration
- Transaction support
- Query optimization

## API Endpoints

### RFQ Endpoints
```typescript
// File: server/routes.ts

// Create RFQ
app.post('/api/rfqs', authenticateApiKey, isAuthenticated, upload.array('documents'), async (req, res) => {
  try {
    const { projectName, projectLocation, dueDate } = req.body;
    const files = req.files as Express.Multer.File[];
    
    // Process uploaded documents
    const processedDocuments = await Promise.all(
      files.map(file => fileProcessingService.processDocument(file))
    );
    
    // Create RFQ with AI-extracted data
    const rfq = await storage.createRfq({
      projectName,
      projectLocation,
      dueDate: new Date(dueDate),
      createdBy: req.user.id,
      extractedData: mergeExtractedData(processedDocuments),
      aiSummary: generateSummary(processedDocuments)
    });
    
    res.status(201).json(rfq);
  } catch (error) {
    console.error('Error creating RFQ:', error);
    res.status(500).json({ message: 'Failed to create RFQ' });
  }
});

// Generate master summary
app.post('/api/rfqs/:rfqId/generate-master-summary', authenticateApiKey, isAuthenticated, async (req, res) => {
  try {
    const { rfqId } = req.params;
    const result = await masterSummaryService.generateMasterSummary(rfqId);
    
    await storage.updateRfq(rfqId, {
      masterSummary: result.masterSummary,
      conflictFlags: result.conflicts,
      summaryGeneratedAt: new Date()
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error generating master summary:', error);
    res.status(500).json({ message: 'Failed to generate master summary' });
  }
});
```

### Bid Analysis Endpoints
```typescript
// Generate bid analysis
app.get('/api/rfqs/:rfqId/bids/analysis', authenticateApiKey, isAuthenticated, async (req, res) => {
  try {
    const { rfqId } = req.params;
    const analysis = await bidAnalysisService.generateBidAnalysis(rfqId);
    res.json({ analysis });
  } catch (error) {
    console.error('Error generating bid analysis:', error);
    res.status(500).json({ message: 'Failed to generate analysis' });
  }
});

// Generate bid comparison report
app.get('/api/rfqs/:rfqId/bid-comparison-report', authenticateApiKey, isAuthenticated, async (req, res) => {
  try {
    const { rfqId } = req.params;
    const { format = 'json' } = req.query;
    
    const report = await enhancedBidComparison.generateBidComparisonReport(rfqId);
    
    if (format === 'pdf') {
      const pdfBuffer = await generatePDFReport(report);
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="bid-comparison-${rfqId}.pdf"`);
      res.send(pdfBuffer);
    } else {
      res.json(report);
    }
  } catch (error) {
    console.error('Error generating bid comparison:', error);
    res.status(500).json({ message: 'Failed to generate report' });
  }
});
```

### API Key Management Endpoints
```typescript
// Create API key
app.post('/api/auth/api-keys', isAuthenticated, async (req, res) => {
  try {
    const { name, permissions, rateLimit } = req.body;
    const apiKey = await apiKeyService.generateApiKey(
      req.user.id,
      name,
      permissions,
      rateLimit
    );
    res.status(201).json(apiKey);
  } catch (error) {
    console.error('Error creating API key:', error);
    res.status(500).json({ message: 'Failed to create API key' });
  }
});

// Get API key usage statistics
app.get('/api/auth/api-keys/:keyId/stats', isAuthenticated, async (req, res) => {
  try {
    const { keyId } = req.params;
    const stats = await apiKeyService.getUsageStats(keyId);
    res.json(stats);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch statistics' });
  }
});
```

## Security Implementation

### Middleware Stack
```typescript
// File: server/middleware/security.ts
export const securityMiddleware = [
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "https:"]
      }
    }
  }),
  cors({
    origin: process.env.FRONTEND_URL,
    credentials: true
  }),
  rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // requests per window
  })
];
```

### Role-Based Access Control
```typescript
// Permission checking middleware
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    
    if (user.apiKey && !hasPermission(user.apiKey.permissions, permission)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    next();
  };
};

// Usage
app.get('/api/rfqs', requirePermission('read'), async (req, res) => {
  // Route handler
});
```

### Audit Logging
```typescript
// File: server/services/auditService.ts
export async function logApiAccess(req: Request, res: Response) {
  const logEntry = {
    userId: req.user?.id,
    apiKeyId: req.user?.apiKey?.id,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date(),
    statusCode: res.statusCode
  };
  
  await db.insert(auditLogs).values(logEntry);
}
```

## Performance Optimization

### Database Optimization
```sql
-- Strategic indexes for optimal query performance
CREATE INDEX idx_rfqs_created_by ON rfqs(created_by);
CREATE INDEX idx_rfqs_status ON rfqs(status);
CREATE INDEX idx_rfqs_due_date ON rfqs(due_date);
CREATE INDEX idx_bids_rfq_id ON bids(rfq_id);
CREATE INDEX idx_bids_contractor_id ON bids(contractor_id);
```

### Query Optimization
```typescript
// Efficient data fetching with joins
export async function getBidsByRfqWithContractors(rfqId: string) {
  return await db
    .select({
      bid: bids,
      contractor: contractors
    })
    .from(bids)
    .innerJoin(contractors, eq(bids.contractorId, contractors.id))
    .where(eq(bids.rfqId, rfqId));
}
```

### Background Processing
```typescript
// File: server/services/progressService.ts
export class ProgressService {
  private jobs = new Map<string, JobProgress>();

  startJob(jobId: string, description: string): void {
    this.jobs.set(jobId, {
      id: jobId,
      description,
      progress: 0,
      status: 'running',
      startedAt: new Date()
    });
  }

  updateProgress(jobId: string, progress: number): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.progress = Math.min(100, Math.max(0, progress));
    }
  }

  async processDocumentWithProgress(file: Express.Multer.File, jobId: string): Promise<ProcessedDocument> {
    this.updateProgress(jobId, 25);
    const extractedText = await extractText(file);
    
    this.updateProgress(jobId, 50);
    const aiData = await extractRFQData(extractedText);
    
    this.updateProgress(jobId, 75);
    const processedDoc = await finalizeDocument(extractedText, aiData);
    
    this.updateProgress(jobId, 100);
    return processedDoc;
  }
}
```

## Error Handling and Logging

### Centralized Error Handler
```typescript
// File: server/middleware/errorHandler.ts
export const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Error:', err);

  if (err instanceof ValidationError) {
    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: err.message,
        details: err.details
      }
    });
  }

  if (err instanceof AuthenticationError) {
    return res.status(401).json({
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: 'Invalid authentication credentials'
      }
    });
  }

  // Default server error
  res.status(500).json({
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred'
    }
  });
};
```

### Structured Logging
```typescript
// File: server/utils/logger.ts
import winston from 'winston';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Usage throughout the application
logger.info('RFQ created', { rfqId, userId, projectName });
logger.error('AI processing failed', { error: error.message, rfqId });
```

## Deployment and Configuration

### Environment Configuration
```typescript
// File: server/config.ts
export const config = {
  port: process.env.PORT || 5000,
  database: {
    url: process.env.DATABASE_URL!,
    ssl: process.env.NODE_ENV === 'production'
  },
  ai: {
    geminiApiKey: process.env.GEMINI_API_KEY!,
    groqApiKey: process.env.GROQ_API_KEY!
  },
  auth: {
    sessionSecret: process.env.SESSION_SECRET!,
    jwtSecret: process.env.JWT_SECRET!
  },
  storage: {
    bucketName: process.env.STORAGE_BUCKET!
  }
};
```

### Health Check Endpoint
```typescript
// Health monitoring
app.get('/health', async (req, res) => {
  try {
    // Check database connectivity
    await db.select().from(rfqs).limit(1);
    
    // Check AI service availability
    const aiHealthy = await checkAIServices();
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'up',
        ai: aiHealthy ? 'up' : 'degraded',
        storage: 'up'
      }
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

## Monitoring and Analytics

### Performance Metrics
```typescript
// File: server/middleware/metrics.ts
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    // Log request metrics
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent')
    });
    
    // Update performance counters
    updateMetrics(req.method, req.url, res.statusCode, duration);
  });
  
  next();
};
```

### API Usage Analytics
```typescript
// File: server/routes/analytics.ts
app.get('/api/analytics/usage', isAuthenticated, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const usage = await db
      .select({
        date: sql`DATE(created_at)`,
        requests: sql`COUNT(*)`,
        avgResponseTime: sql`AVG(response_time)`
      })
      .from(auditLogs)
      .where(
        and(
          gte(auditLogs.createdAt, new Date(startDate as string)),
          lte(auditLogs.createdAt, new Date(endDate as string))
        )
      )
      .groupBy(sql`DATE(created_at)`)
      .orderBy(sql`DATE(created_at)`);
    
    res.json({ usage });
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch analytics' });
  }
});
```

### Contractor Performance Analytics
```typescript
// File: server/routes.ts - Contractor Performance Endpoint
app.get('/api/contractors/performance-stats', isAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.sub;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get all bids for this contractor
    const allBids = await storage.getBidsByContractor(userId);
    
    // Calculate key metrics
    const totalBidsSubmitted = allBids.length;
    const acceptedBids = allBids.filter(bid => bid.status === 'accept');
    const totalBidsWon = acceptedBids.length;
    const winRate = totalBidsSubmitted > 0 ? (totalBidsWon / totalBidsSubmitted) * 100 : 0;
    
    // Calculate average bid amount from ALL submitted bids (not just won bids)
    const averageBidAmount = totalBidsSubmitted > 0 
      ? allBids.reduce((sum, bid) => {
          const amount = bid.bidAmount || bid.extractedAmount || 0;
          return sum + parseFloat(amount.toString());
        }, 0) / totalBidsSubmitted
      : 0;
    
    // Calculate total earnings from accepted bids only
    const totalEarnings = acceptedBids.reduce((sum, bid) => {
      const amount = bid.bidAmount || bid.extractedAmount || 0;
      return sum + parseFloat(amount.toString());
    }, 0);

    // Get latest bid for recent activity
    const latestBid = allBids.length > 0 ? allBids[0] : null;
    
    res.json({
      totalBidsSubmitted,
      totalBidsWon,
      winRate: parseFloat(winRate.toFixed(2)),
      averageBidAmount: parseFloat(averageBidAmount.toFixed(2)),
      totalEarnings: parseFloat(totalEarnings.toFixed(2)),
      latestBid: latestBid ? {
        amount: latestBid.bidAmount || latestBid.extractedAmount || 0,
        status: latestBid.status,
        submittedAt: latestBid.createdAt
      } : null,
      // Additional analytics data...
    });
  } catch (error) {
    console.error('Performance stats error:', error);
    res.status(500).json({ message: 'Failed to fetch performance stats' });
  }
});
```

**Recent Fixes (July 28, 2025)**:
- **Fixed Bid Amount Calculation**: Changed from non-existent `totalBidAmount` to use `bidAmount` and `extractedAmount` properties
- **Corrected Average Calculation**: Now calculates average from ALL submitted bids instead of only won bids for accurate metrics
- **Enhanced Data Quality**: Added proper null checks and fallback values for bid amounts
- **TypeScript Compliance**: Resolved compilation errors related to bid property access and type safety

## Testing

### Unit Testing
```typescript
// File: __tests__/services/aiService.test.ts
import { extractRFQData } from '../services/aiService';

describe('AI Service', () => {
  it('should extract project name from document text', async () => {
    const mockText = 'Project: Office Renovation\nLocation: Los Angeles, CA';
    const result = await extractRFQData(mockText);
    
    expect(result.projectName).toBe('Office Renovation');
    expect(result.projectLocation).toBe('Los Angeles, CA');
  });
});
```

### Integration Testing
```typescript
// File: __tests__/integration/rfq.test.ts
import request from 'supertest';
import { app } from '../server';

describe('RFQ API', () => {
  it('should create RFQ with documents', async () => {
    const response = await request(app)
      .post('/api/rfqs')
      .field('projectName', 'Test Project')
      .attach('documents', 'test-document.pdf')
      .expect(201);
    
    expect(response.body.projectName).toBe('Test Project');
    expect(response.body.id).toBeDefined();
  });
});
```

## Best Practices

### Code Organization
- Service layer for business logic
- Middleware for cross-cutting concerns
- Repository pattern for data access
- Dependency injection for testability

### Security Best Practices
- Input validation on all endpoints
- SQL injection prevention with parameterized queries
- Rate limiting to prevent abuse
- Comprehensive audit logging
- Secure file upload handling

### Performance Best Practices
- Database query optimization
- Caching strategy implementation
- Background job processing
- Resource cleanup and memory management

### Monitoring Best Practices
- Structured logging throughout the application
- Performance metrics collection
- Error tracking and alerting
- Health check endpoints for monitoring

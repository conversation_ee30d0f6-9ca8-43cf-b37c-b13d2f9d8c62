import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Users, TrendingUp, FileText, Brain, Clock, DollarSign, Shield, AlertCircle, Play } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import db1Image from "@assets/db1_1752928088356.png";
import aiBidAnalysisImage from "@assets/db3_1752932237878.png";
import db2Image from "@assets/db2_1752928088355.png";
import constructionHeroBg from "@assets/construction_hero_bg.jpg";

export default function Landing() {
  const [email, setEmail] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [waitlistCount, setWaitlistCount] = useState(17); // Starting count
  const { toast } = useToast();

  // Fetch current waitlist count
  useEffect(() => {
    fetch('/api/waitlist/count')
      .then(res => res.json())
      .then(data => {
        if (data.count && typeof data.count === 'number' && !isNaN(data.count)) {
          setWaitlistCount(data.count);
        }
      })
      .catch(() => {}); // Silent fail, use default count
  }, []);

  const handleWaitlistSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/waitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          companyName,
          firstName,
          lastName,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubmitted(true);
        setWaitlistCount(data.position);
        toast({
          title: "Welcome to the waitlist!",
          description: `You're #${data.position} in line. We'll notify you when Bidaible is ready.`,
        });
      } else {
        toast({
          title: "Error",
          description: data.message || "Failed to join waitlist",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const remainingSpots = Math.max(0, 100 - (typeof waitlistCount === 'number' && !isNaN(waitlistCount) ? waitlistCount : 47));

  return (
    <div className="min-h-screen">
      {/* Hero Section with Background */}
      <div 
        className="relative min-h-screen bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${constructionHeroBg})` }}
      >
        {/* Dark overlay for text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/45 via-black/35 to-black/55"></div>
        
        {/* Hero Content */}
        <div className="relative z-10 container mx-auto px-4 py-16">
          <div className="max-w-7xl mx-auto">
            {/* Main Content Area */}
            <div className="text-center mb-16">
              <Badge variant="destructive" className="mb-6 text-sm font-medium bg-primary text-primary-foreground">
                <AlertCircle className="w-4 h-4 mr-1" />
                Limited Early Access - {remainingSpots} spots remaining
              </Badge>

              <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 sm:mb-8 leading-tight px-4 sm:px-0">
                <span className="font-semibold">WIN THE BID</span>
                <br />
                Stop drowning in spreadsheets <br />
                and email chains.
              </h1>

              <p className="text-lg sm:text-xl md:text-2xl leading-relaxed max-w-4xl mx-auto text-white/90 sm:text-[#2a2c37] text-center sm:text-left mt-4 mb-4 px-4 sm:mt-[17px] sm:mb-[17px] sm:ml-[120px] sm:mr-[120px] lg:ml-[200px] lg:mr-[200px] xl:ml-[300px] xl:mr-[300px]">
               The smart way to manage multiple bids. 
                <br className="hidden sm:block" /> <span className="sm:hidden"> </span>Send RFQs to the right contractors. 
                <br className="hidden sm:block" /> <span className="sm:hidden"> </span>Get AI insights on every proposal.
                <br className="hidden sm:block" /> <span className="sm:hidden"> </span>Award projects with confidence.
              </p>

              <p className="text-base sm:text-lg max-w-3xl mx-auto text-white/80 sm:text-[#2a2c37] text-center mt-6 mb-8 px-4 sm:mt-[23px] sm:mb-[23px]">
                Transform your RFQ-to-award process with our subcontractor network, 
               <br className="hidden sm:block" /> <span className="sm:hidden"> </span>AI-powered bid evaluation, and real-time pricing updates.
              </p>

              {/* See How It Works Button */}
              <div className="mb-20">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button size="lg" className="text-lg sm:text-xl px-8 sm:px-12 py-6 sm:py-8 bg-primary hover:bg-primary/90 shadow-lg text-white">
                      <Play className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3" />
                      See How It Works
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Bidaible Dashboard Preview</DialogTitle>
                      <DialogDescription>
                        See how Bidaible streamlines your bidding process
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-semibold mb-2">RFQ Management Dashboard</h4>
                        <div className="rounded-lg overflow-hidden border">
                          <img 
                            src={db1Image} 
                            alt="RFQ Management Dashboard showing active RFQs with project details and status tracking"
                            className="w-full h-auto"
                          />
                          <p className="text-xs text-muted-foreground text-center p-2 bg-muted/30">Track all your RFQs, bids, and project status in one place</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">AI-Powered Bid Analysis</h4>
                        <div className="rounded-lg overflow-hidden border">
                          <img 
                            src={aiBidAnalysisImage} 
                            alt="AI-powered bid analysis showing extraction results, timeline, bid summary, and cost breakdown"
                            className="w-full h-auto"
                          />
                          <p className="text-xs text-muted-foreground text-center p-2 bg-muted/30">Get instant AI insights on bid quality, pricing, and comprehensive project analysis</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Contractor Network</h4>
                        <div className="rounded-lg overflow-hidden border">
                          <img 
                            src={db2Image} 
                            alt="Contractor Network showing qualified contractors with ratings and specialties"
                            className="w-full h-auto"
                          />
                          <p className="text-xs text-muted-foreground text-center p-2 bg-muted/30">Connect with qualified contractors and manage your favorites</p>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                <p className="mt-4 text-base sm:text-lg text-white/80 sm:text-[#2a2c37] px-4">
                  View details from the platform
                </p>
              </div>
            </div>



            {/* Waitlist Form - Centered */}
            <div className="flex justify-center pb-16">
              <div className="w-full max-w-md">
                {!isSubmitted ? (
                  <Card className="bg-white/95 backdrop-blur-sm shadow-xl border-primary/20">
                    <CardHeader className="text-center">
                      <CardTitle className="text-xl">Be among the first 100 users</CardTitle>
                      <CardDescription>
                        Join {waitlistCount} contractors and GCs already in line
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <form onSubmit={handleWaitlistSubmit} className="space-y-4">
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <Label htmlFor="firstName" className="sr-only">First Name</Label>
                            <Input
                              id="firstName"
                              type="text"
                              placeholder="First name"
                              value={firstName}
                              onChange={(e) => setFirstName(e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="lastName" className="sr-only">Last Name</Label>
                            <Input
                              id="lastName"
                              type="text"
                              placeholder="Last name"
                              value={lastName}
                              onChange={(e) => setLastName(e.target.value)}
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="email" className="sr-only">Email</Label>
                          <Input
                            id="email"
                            type="email"
                            placeholder="Enter your email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="companyName" className="sr-only">Company Name</Label>
                          <Input
                            id="companyName"
                            type="text"
                            placeholder="Company name"
                            value={companyName}
                            onChange={(e) => setCompanyName(e.target.value)}
                          />
                        </div>
                        <Button 
                          type="submit" 
                          className="w-full text-lg py-6" 
                          disabled={isSubmitting}
                          size="lg"
                        >
                          {isSubmitting ? "Joining..." : "Join the Waitlist"}
                        </Button>
                        <p className="text-xs text-muted-foreground text-center">
                          No spam. We'll only email you when Bidaible is ready.
                        </p>
                      </form>
                    </CardContent>
                  </Card>
                ) : (
                  <Card className="bg-white/95 backdrop-blur-sm shadow-xl border-green-200">
                    <CardContent className="text-center py-6">
                      <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-green-800 mb-2">
                        You're in! 🎉
                      </h3>
                      <p className="text-green-700">
                        You're #{waitlistCount} in line. We'll notify you as soon as Bidaible is ready.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Rest of the content with normal background */}
      <div className="bg-gradient-to-br from-background to-muted">
        <div className="container mx-auto px-4 py-16">

        {/* Problem/Solution Section */}
        <div className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-4 text-foreground">The current way is broken...</h2>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">❌</span>
                  Drowning in email chains and spreadsheets during bid management
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">❌</span>
                  Missing qualified contractors or receiving poor-quality bids
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">❌</span>
                  Time-consuming manual bid comparison and analysis
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">❌</span>
                  Uncertainty about fair pricing and contractor capabilities
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">❌</span>
                  Constantly recalculating after each bid is received
                </li>
              </ul>
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-4 text-primary">Here's what changes...</h2>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start">
                  <CheckCircle className="text-green-500 mr-2 mt-0.5 h-5 w-5" />
                  <span><strong>60% faster</strong> RFQ preparation and distribution</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="text-green-500 mr-2 mt-0.5 h-5 w-5" />
                  <span><strong>Smart matching</strong> connects you with qualified contractors</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="text-green-500 mr-2 mt-0.5 h-5 w-5" />
                  <span><strong>AI analysis</strong> evaluates bids for best value, not just lowest price</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="text-green-500 mr-2 mt-0.5 h-5 w-5" />
                  <span><strong>One platform</strong> from RFQ creation to contract award</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="text-green-500 mr-2 mt-0.5 h-5 w-5" />
                  <span><strong>Real-time pricing</strong> with up-to-the-minute RFQ totals</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Redesigned Features Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Everything you need to win more bids
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Streamlined tools that save time, improve outcomes, and give you confidence in every decision
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <CardTitle className="text-xl">60% Time Savings</CardTitle>
                <CardDescription className="text-base">
                  Slash RFQ preparation time with automated document processing and smart contractor matching
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
                <CardTitle className="text-xl">AI-Powered Analysis</CardTitle>
                <CardDescription className="text-base">
                  Get intelligent bid comparisons that evaluate quality, risk, and value - not just price
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                </div>
                <CardTitle className="text-xl">Smart Contractor Network</CardTitle>
                <CardDescription className="text-base">
                  Connect with pre-qualified contractors and build your favorites list for repeat projects
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
                <CardTitle className="text-xl">One Platform Solution</CardTitle>
                <CardDescription className="text-base">
                  Manage everything from RFQ creation to contract award in a single, integrated workflow
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="h-8 w-8 text-red-600 dark:text-red-400" />
                </div>
                <CardTitle className="text-xl">Real-Time Pricing</CardTitle>
                <CardDescription className="text-base">
                  See live project totals that update automatically as bids come in
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
                </div>
                <CardTitle className="text-xl">Risk Assessment</CardTitle>
                <CardDescription className="text-base">
                  Built-in risk scoring helps you identify the most reliable contractors and bids
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mb-16 max-w-3xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-muted-foreground">
              Everything you need to know about Bidaible
            </p>
          </div>

          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger className="text-left">
                How does Bidaible save me 60% of my time on RFQs?
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                Bidaible automates document processing, extracts key project details with AI, and automatically distributes RFQs to qualified contractors. Instead of spending hours manually preparing and sending RFQs, you can create and distribute them in minutes.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2">
              <AccordionTrigger className="text-left">
                What makes the AI bid analysis better than manual review?
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                Our AI analyzes bids across multiple criteria including technical capability, pricing competitiveness, timeline feasibility, and risk factors. It provides objective comparisons and highlights potential red flags that might be missed in manual review, helping you choose the best value, not just the lowest price.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3">
              <AccordionTrigger className="text-left">
                How does the contractor network work?
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                We maintain a network of pre-qualified contractors across different trades and regions. You can search by location, trade type, and project size, then build your own favorites list for repeat projects. The platform tracks contractor performance to help you make informed decisions.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4">
              <AccordionTrigger className="text-left">
                Is my project data secure?
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                Yes, absolutely. We use enterprise-grade security with encrypted data storage and transmission. Your project information is only shared with contractors you specifically invite to bid. We maintain strict access controls and audit trails for all activities.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5">
              <AccordionTrigger className="text-left">
                What types of projects work best with Bidaible?
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                Bidaible works great for commercial construction, tenant improvements, renovations, and infrastructure projects. Whether you're a general contractor seeking subcontractors or a project owner looking for general contractors, the platform adapts to your workflow.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-6">
              <AccordionTrigger className="text-left">
                When will Bidaible be available?
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                We're launching with a limited group of 100 early users to ensure the best possible experience. Early access members will get priority support, influence on feature development, and special pricing. Join the waitlist to be notified as soon as we're ready.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

          {/* Final CTA */}
          <div className="text-center">
            <Card className="max-w-2xl mx-auto bg-primary/5 border-primary/20">
              <CardHeader>
                <CardTitle className="text-2xl">Ready to transform your bidding process?</CardTitle>
                <CardDescription className="text-lg">
                  {remainingSpots > 0 ? (
                    <>Only {remainingSpots} early access spots remaining</>
                  ) : (
                    <>Join {waitlistCount}+ professionals already waiting</>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!isSubmitted ? (
                  <Button 
                    size="lg" 
                    className="text-lg px-8 py-6"
                    onClick={() => document.querySelector('input[type="email"]')?.scrollIntoView({ behavior: 'smooth' })}
                  >
                    Join the Waitlist
                  </Button>
                ) : (
                  <div className="flex items-center justify-center gap-2 text-green-600">
                    <CheckCircle className="w-5 h-5" />
                    <span className="text-lg font-medium">You're all set!</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      {/* Developer Login */}
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          className="text-xs bg-background border-2 border-primary/20 text-primary hover:bg-primary hover:text-primary-foreground shadow-lg"
          onClick={() => window.location.href = '/api/login'}
        >
          🔐 Dev Login
        </Button>
      </div>
    </div>
  );
}
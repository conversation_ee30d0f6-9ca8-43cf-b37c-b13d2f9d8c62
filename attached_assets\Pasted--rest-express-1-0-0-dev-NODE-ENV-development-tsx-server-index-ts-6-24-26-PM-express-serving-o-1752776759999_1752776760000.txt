
> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

6:24:26 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 9 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
6:24:31 PM [express] GET /api/auth/user 401 in 2ms :: {"message":"Unauthorized"}
6:24:34 PM [express] GET /api/auth/user 304 in 460ms :: {"id":"34020394","email":"roy.gatling@snapz.…
6:24:35 PM [express] GET /api/dashboard/stats 304 in 302ms :: {"activeRfqs":"3","totalBids":"1","avg…
6:24:35 PM [express] GET /api/rfqs 304 in 579ms :: [{"id":"747e0b12-fa33-474e-9f0d-d14fef261200","cr…
Found 1 bid results, returning 1 unique bids
6:25:49 PM [express] GET /api/contractors/bids 304 in 1034ms :: [{"id":"8565427d-6333-454a-bb11-0047…
6:25:49 PM [express] GET /api/rfqs/747e0b12-fa33-474e-9f0d-d14fef261200/documents 304 in 507ms :: [{…
6:25:49 PM [express] GET /api/rfqs/1425b14f-95da-4450-9d5f-5c3905b8ce22/documents 304 in 660ms :: []
6:25:49 PM [express] GET /api/rfqs/a3f3534a-8d65-46dd-be35-6fc5ec8920f7/documents 304 in 666ms :: [{…
file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:395
0&&(o=t[0]),o instanceof Error)throw o;var u=new Error("Unhandled error."+(o?" ("+
                               ^

error: terminating connection due to administrator command
    at cn.parseErrorMessage (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1182:6)
    at cn.handlePacket (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1143:13)
    at cn.parse (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1127:36)
    at v.<anonymous> (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1190:16)
    at v.emit (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:397:12)
    at WebSocket.<anonymous> (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:987:80)
    at callListener (/home/<USER>/workspace/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/home/<USER>/workspace/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:518:28)
    at Receiver.receiverOnMessage (/home/<USER>/workspace/node_modules/ws/lib/websocket.js:1220:20)
    at Receiver.emit (node:events:518:28)
    at Receiver.dataMessage (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:569:14)
    at Receiver.getData (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:496:10)
    at Receiver.startLoop (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:167:16)
    at Receiver._write (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:94:10)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at TLSSocket.socketOnData (/home/<USER>/workspace/node_modules/ws/lib/websocket.js:1355:35)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Readable.push (node:internal/streams/readable:392:5)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23) {
  length: 116,
  severity: 'FATAL',
  code: '57P01',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'postgres.c',
  line: '3289',
  routine: 'ProcessInterrupts',
  client: NeonClient {
    _events: [Object: null prototype] { error: [Function (anonymous)] },
    _eventsCount: 1,
    _maxListeners: undefined,
    connectionParameters: ConnectionParameters {
      user: 'neondb_owner',
      database: 'neondb',
      port: 5432,
      host: 'ep-cool-haze-afoo1btj.c-2.us-west-2.aws.neon.tech',
      binary: false,
      options: undefined,
      ssl: {},
      client_encoding: '',
      replication: undefined,
      isDomainSocket: false,
      application_name: undefined,
      fallback_application_name: undefined,
      statement_timeout: false,
      lock_timeout: false,
      idle_in_transaction_session_timeout: false,
      query_timeout: false,
      connect_timeout: 0
    },
    user: 'neondb_owner',
    database: 'neondb',
    port: 5432,
    host: 'ep-cool-haze-afoo1btj.c-2.us-west-2.aws.neon.tech',
    replication: undefined,
    _Promise: [Function: Promise],
    _types: TypeOverrides {
      _types: {
        getTypeParser: [Function: getTypeParser],
        setTypeParser: [Function: setTypeParser],
        arrayParser: { create: [Function: create] },
        builtins: {
          BOOL: 16,
          BYTEA: 17,
          CHAR: 18,
          INT8: 20,
          INT2: 21,
          INT4: 23,
          REGPROC: 24,
          TEXT: 25,
          OID: 26,
          TID: 27,
          XID: 28,
          CID: 29,
          JSON: 114,
          XML: 142,
          PG_NODE_TREE: 194,
          SMGR: 210,
          PATH: 602,
          POLYGON: 604,
          CIDR: 650,
          FLOAT4: 700,
          FLOAT8: 701,
          ABSTIME: 702,
          RELTIME: 703,
          TINTERVAL: 704,
          CIRCLE: 718,
          MACADDR8: 774,
          MONEY: 790,
          MACADDR: 829,
          INET: 869,
          ACLITEM: 1033,
          BPCHAR: 1042,
          VARCHAR: 1043,
          DATE: 1082,
          TIME: 1083,
          TIMESTAMP: 1114,
          TIMESTAMPTZ: 1184,
          INTERVAL: 1186,
          TIMETZ: 1266,
          BIT: 1560,
          VARBIT: 1562,
          NUMERIC: 1700,
          REFCURSOR: 1790,
          REGPROCEDURE: 2202,
          REGOPER: 2203,
          REGOPERATOR: 2204,
          REGCLASS: 2205,
          REGTYPE: 2206,
          UUID: 2950,
          TXID_SNAPSHOT: 2970,
          PG_LSN: 3220,
          PG_NDISTINCT: 3361,
          PG_DEPENDENCIES: 3402,
          TSVECTOR: 3614,
          TSQUERY: 3615,
          GTSVECTOR: 3642,
          REGCONFIG: 3734,
          REGDICTIONARY: 3769,
          JSONB: 3802,
          REGNAMESPACE: 4089,
          REGROLE: 4096
        }
      },
      text: {},
      binary: {}
    },
    _ending: true,
    _connecting: false,
    _connected: true,
    _connectionError: false,
    _queryable: false,
    connection: Connection {
      _events: [Object: null prototype] {
        newListener: [Function (anonymous)],
        connect: [ [Function (anonymous)], [Function (anonymous)] ],
        sslconnect: [Function (anonymous)],
        authenticationMD5Password: [Function: bound _handleAuthMD5Password],
        authenticationSASL: [Function: bound _handleAuthSASL],
        authenticationSASLContinue: [Function: bound _handleAuthSASLContinue] AsyncFunction,
        authenticationSASLFinal: [Function: bound _handleAuthSASLFinal],
        backendKeyData: [Function: bound _handleBackendKeyData],
        error: [Function: bound _handleErrorEvent],
        errorMessage: [Function: bound _handleErrorMessage],
        notice: [Function: bound _handleNotice],
        rowDescription: [Function: bound _handleRowDescription],
        dataRow: [Function: bound _handleDataRow],
        portalSuspended: [Function: bound _handlePortalSuspended],
        emptyQuery: [Function: bound _handleEmptyQuery],
        commandComplete: [Function: bound _handleCommandComplete],
        parseComplete: [Function: bound _handleParseComplete],
        copyInResponse: [Function: bound _handleCopyInResponse],
        copyData: [Function: bound _handleCopyData],
        notification: [Function: bound _handleNotification],
        end: [
          [Function: bound onceWrapper] {
            listener: [Function (anonymous)]
          },
          [Function: bound onceWrapper] {
            listener: [Function (anonymous)]
          }
        ],
        readyForQuery: [Function: bound _handleReadyForQuery]
      },
      _eventsCount: 22,
      _maxListeners: undefined,
      stream: Socket {
        _events: [Object: null prototype] {
          error: [Function: reportStreamError],
          close: [Function (anonymous)],
          end: [ [Function (anonymous)], [Function (anonymous)] ],
          data: [Function (anonymous)]
        },
        _eventsCount: 4,
        _maxListeners: undefined,
        opts: {},
        connecting: false,
        pending: false,
        writable: true,
        encrypted: false,
        authorized: false,
        destroyed: true,
        ws: <ref *1> WebSocket {
          _events: [Object: null prototype] {
            error: [Function],
            message: [Function],
            close: [Function],
            open: [Function]
          },
          _eventsCount: 4,
          _maxListeners: undefined,
          _binaryType: 'arraybuffer',
          _closeCode: 1006,
          _closeFrameReceived: false,
          _closeFrameSent: false,
          _closeMessage: Buffer(0) [Uint8Array] [],
          _closeTimer: Timeout {
            _idleTimeout: 30000,
            _idlePrev: [TimersList],
            _idleNext: [Timeout],
            _idleStart: 85684,
            _onTimeout: [Function: bound ],
            _timerArgs: undefined,
            _repeat: null,
            _destroyed: false,
            [Symbol(refed)]: true,
            [Symbol(kHasPrimitive)]: false,
            [Symbol(asyncId)]: 9214,
            [Symbol(triggerId)]: 9037
          },
          _errorEmitted: false,
          _extensions: {},
          _paused: false,
          _protocol: '',
          _readyState: 2,
          _receiver: Receiver {
            _events: [Object],
            _writableState: [WritableState],
            _maxListeners: undefined,
            _allowSynchronousEvents: true,
            _binaryType: 'arraybuffer',
            _extensions: {},
            _isServer: false,
            _maxPayload: 104857600,
            _skipUTF8Validation: false,
            _bufferedBytes: 0,
            _buffers: [],
            _compressed: false,
            _payloadLength: 117,
            _mask: undefined,
            _fragmented: 0,
            _masked: false,
            _fin: true,
            _opcode: 2,
            _totalPayloadLength: 0,
            _messageLength: 0,
            _fragments: [],
            _errored: false,
            _loop: true,
            _state: 4,
            _eventsCount: 6,
            [Symbol(shapeMode)]: true,
            [Symbol(kCapture)]: false,
            [Symbol(websocket)]: [Circular *1]
          },
          _sender: Sender {
            _extensions: {},
            _socket: [TLSSocket],
            _firstFragment: true,
            _compress: false,
            _bufferedBytes: 0,
            _queue: [],
            _state: 0,
            onerror: [Function: senderOnError],
            [Symbol(websocket)]: [Circular *1]
          },
          _socket: TLSSocket {
            _tlsOptions: [Object],
            _secureEstablished: true,
            _securePending: false,
            _newSessionPending: false,
            _controlReleased: true,
            secureConnecting: false,
            _SNICallback: null,
            servername: 'ep-cool-haze-afoo1btj.c-2.us-west-2.aws.neon.tech',
            alpnProtocol: false,
            authorized: true,
            authorizationError: null,
            encrypted: true,
            _events: [Object: null prototype],
            _eventsCount: 6,
            connecting: false,
            _hadError: false,
            _parent: null,
            _host: 'ep-cool-haze-afoo1btj.c-2.us-west-2.aws.neon.tech',
            _closeAfterHandlingError: false,
            _readableState: [ReadableState],
            _writableState: [WritableState],
            allowHalfOpen: false,
            _maxListeners: undefined,
            _sockname: null,
            _pendingData: null,
            _pendingEncoding: '',
            server: undefined,
            _server: null,
            ssl: [TLSWrap],
            _requestCert: true,
            _rejectUnauthorized: true,
            parser: null,
            _httpMessage: null,
            autoSelectFamilyAttemptedAddresses: [Array],
            timeout: 0,
            [Symbol(alpncallback)]: null,
            [Symbol(res)]: [TLSWrap],
            [Symbol(verified)]: true,
            [Symbol(pendingSession)]: null,
            [Symbol(async_id_symbol)]: 9037,
            [Symbol(kHandle)]: [TLSWrap],
            [Symbol(lastWriteQueueSize)]: 6,
            [Symbol(timeout)]: null,
            [Symbol(kBuffer)]: null,
            [Symbol(kBufferCb)]: null,
            [Symbol(kBufferGen)]: null,
            [Symbol(shapeMode)]: true,
            [Symbol(kCapture)]: false,
            [Symbol(kSetNoDelay)]: true,
            [Symbol(kSetKeepAlive)]: false,
            [Symbol(kSetKeepAliveInitialDelay)]: 0,
            [Symbol(kBytesRead)]: 0,
            [Symbol(kBytesWritten)]: 0,
            [Symbol(connect-options)]: [Object],
            [Symbol(websocket)]: [Circular *1]
          },
          _bufferedAmount: 0,
          _isServer: false,
          _redirects: 0,
          _autoPong: true,
          _url: 'wss://ep-cool-haze-afoo1btj.c-2.us-west-2.aws.neon.tech/v2',
          _req: null,
          [Symbol(shapeMode)]: false,
          [Symbol(kCapture)]: false
        },
        writeBuffer: undefined,
        tlsState: 0,
        tlsRead: undefined,
        tlsWrite: undefined
      },
      _keepAlive: false,
      _keepAliveInitialDelayMillis: 0,
      lastBuffer: false,
      parsedStatements: {},
      ssl: false,
      _ending: true,
      _emitMessage: false,
      _connecting: true
    },
    queryQueue: [],
    binary: false,
    processID: -67674762,
    secretKey: 362629965,
    ssl: false,
    _connectionTimeoutMillis: 0,
    config: {
      connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
      max: 10,
      maxUses: Infinity,
      allowExitOnIdle: false,
      maxLifetimeSeconds: 0,
      idleTimeoutMillis: 10000
    },
    _connectionCallback: null,
    release: [Function (anonymous)],
    activeQuery: null,
    readyForQuery: true,
    hasExecuted: true,
    _poolUseCount: 2
  }
}

Node.js v20.18.1

import { db } from "../db";
import { bids, contractors, rfqs } from "@shared/schema";
import { eq } from "drizzle-orm";
import { callGroqAPI } from "./aiService";

export interface BidComparison {
  bidId: string;
  contractorName: string;
  bidAmount: number;
  inclusions: string[];
  exclusions: string[];
  keyDifferentiators: string[];
  riskFactors: string[];
  timeline: string;
  warranty: string;
  confidence: number;
}

export interface ComparisonReport {
  comparisons: BidComparison[];
  report: string;
  recommendations: string[];
  totalBids: number;
  averageBid: number;
  lowestBid: number;
  highestBid: number;
}

export async function generateBidComparisonReport(rfqId: string): Promise<ComparisonReport> {
  try {
    console.log(`[BidComparison] Generating report for RFQ: ${rfqId}`);
    
    // Get all bids with contractors for this RFQ
    const bidResults = await db
      .select({
        bid: bids,
        contractor: contractors
      })
      .from(bids)
      .leftJoin(contractors, eq(bids.contractorId, contractors.id))
      .where(eq(bids.rfqId, rfqId));

    if (bidResults.length === 0) {
      return {
        comparisons: [],
        report: "No bids submitted for comparison.",
        recommendations: [],
        totalBids: 0,
        averageBid: 0,
        lowestBid: 0,
        highestBid: 0
      };
    }

    // Process each bid for comparison
    const comparisons = await Promise.all(
      bidResults.map(async ({ bid, contractor }) => {
        const analysis = await analyzeBidInclusionsExclusions(bid);
        const bidAmount = parseFloat(bid.bidAmount || '0');
        
        return {
          bidId: bid.id,
          contractorName: contractor?.companyName || 'Unknown Contractor',
          bidAmount,
          inclusions: analysis.inclusions,
          exclusions: analysis.exclusions,
          keyDifferentiators: analysis.differentiators,
          riskFactors: analysis.risks,
          timeline: bid.timeline || 'Not specified',
          warranty: bid.warranty || 'Standard',
          confidence: parseFloat(bid.confidenceScore || '0')
        };
      })
    );

    // Calculate statistics
    const bidAmounts = comparisons.map(c => c.bidAmount).filter(amount => amount > 0);
    const totalBids = comparisons.length;
    const averageBid = bidAmounts.length > 0 ? bidAmounts.reduce((a, b) => a + b, 0) / bidAmounts.length : 0;
    const lowestBid = bidAmounts.length > 0 ? Math.min(...bidAmounts) : 0;
    const highestBid = bidAmounts.length > 0 ? Math.max(...bidAmounts) : 0;

    // Generate comprehensive comparison report
    const report = await generateComparisonReport(comparisons, { averageBid, lowestBid, highestBid });
    const recommendations = generateRecommendations(comparisons, { averageBid, lowestBid, highestBid });

    return {
      comparisons,
      report,
      recommendations,
      totalBids,
      averageBid,
      lowestBid,
      highestBid
    };
    
  } catch (error) {
    console.error(`[BidComparison] Error generating report:`, error);
    throw error;
  }
}

async function analyzeBidInclusionsExclusions(bid: any) {
  try {
    const prompt = `
Analyze this construction bid submission and extract:
1. What is specifically INCLUDED in the bid price
2. What is specifically EXCLUDED from the bid price  
3. Key differentiators that make this bid unique
4. Potential risk factors or concerns

Bid Information:
- Bid Amount: ${bid.bidAmount || 'Not specified'}
- Scope: ${bid.scope || 'Not specified'}
- Conditions: ${bid.conditions || 'Not specified'}
- Timeline: ${bid.timeline || 'Not specified'}
- Extracted Text: ${bid.extractedText?.substring(0, 1000) || 'No additional text'}

Format your response as JSON with these exact keys:
{
  "inclusions": ["item1", "item2", ...],
  "exclusions": ["item1", "item2", ...], 
  "differentiators": ["point1", "point2", ...],
  "risks": ["risk1", "risk2", ...]
}
`;

    const response = await callGroqAPI(prompt, 'bid-analysis');
    
    try {
      const parsed = JSON.parse(response);
      return {
        inclusions: parsed.inclusions || [],
        exclusions: parsed.exclusions || [],
        differentiators: parsed.differentiators || [],
        risks: parsed.risks || []
      };
    } catch (parseError) {
      console.warn('Failed to parse AI response, using fallback analysis');
      return fallbackAnalysis(bid);
    }
    
  } catch (error) {
    console.error('AI analysis failed, using fallback:', error);
    return fallbackAnalysis(bid);
  }
}

function fallbackAnalysis(bid: any) {
  return {
    inclusions: bid.scope ? [bid.scope.substring(0, 100)] : ['Scope as specified in bid documents'],
    exclusions: ['Items not explicitly mentioned in scope'],
    differentiators: bid.conditions ? [bid.conditions.substring(0, 100)] : ['Standard terms and conditions'],
    risks: bid.bidAmount ? [] : ['Bid amount not clearly specified']
  };
}

async function generateComparisonReport(comparisons: BidComparison[], stats: any): Promise<string> {
  const prompt = `
Generate a comprehensive bid comparison report for these construction bids:

${comparisons.map((comp, idx) => `
Bid ${idx + 1}: ${comp.contractorName}
- Amount: $${comp.bidAmount.toLocaleString()}
- Timeline: ${comp.timeline}
- Warranty: ${comp.warranty}
- Inclusions: ${comp.inclusions.slice(0, 3).join(', ')}
- Exclusions: ${comp.exclusions.slice(0, 3).join(', ')}
- Key Differentiators: ${comp.keyDifferentiators.slice(0, 2).join(', ')}
- Risk Factors: ${comp.riskFactors.slice(0, 2).join(', ')}
`).join('\n')}

Statistics:
- Total Bids: ${stats.totalBids}
- Average Bid: $${stats.averageBid.toLocaleString()}
- Lowest Bid: $${stats.lowestBid.toLocaleString()}
- Highest Bid: $${stats.highestBid.toLocaleString()}

Create a detailed comparison report focusing on:
1. Price analysis and competitiveness
2. Scope coverage comparison
3. Timeline feasibility assessment
4. Risk assessment summary
5. Value proposition analysis

Format as HTML for display.
`;

  try {
    return await callGroqAPI(prompt, 'comparison-report');
  } catch (error) {
    return generateFallbackReport(comparisons, stats);
  }
}

function generateRecommendations(comparisons: BidComparison[], stats: any): string[] {
  const recommendations: string[] = [];
  
  // Price-based recommendations
  const priceDiff = stats.highestBid - stats.lowestBid;
  const priceVariance = priceDiff / stats.averageBid;
  
  if (priceVariance > 0.3) {
    recommendations.push("High price variance detected - investigate scope differences");
  }
  
  // Risk-based recommendations
  const highRiskBids = comparisons.filter(c => c.riskFactors.length > 2);
  if (highRiskBids.length > 0) {
    recommendations.push(`${highRiskBids.length} bid(s) have multiple risk factors - review carefully`);
  }
  
  // Timeline recommendations
  const noTimelineBids = comparisons.filter(c => c.timeline === 'Not specified');
  if (noTimelineBids.length > 0) {
    recommendations.push("Request timeline clarification from contractors with unspecified schedules");
  }
  
  if (recommendations.length === 0) {
    recommendations.push("All bids appear complete - proceed with detailed evaluation");
  }
  
  return recommendations;
}

function generateFallbackReport(comparisons: BidComparison[], stats: any): string {
  return `
    <div>
      <h3>Bid Comparison Summary</h3>
      <p><strong>Total Bids:</strong> ${comparisons.length}</p>
      <p><strong>Price Range:</strong> $${stats.lowestBid.toLocaleString()} - $${stats.highestBid.toLocaleString()}</p>
      <p><strong>Average Bid:</strong> $${stats.averageBid.toLocaleString()}</p>
      
      <h4>Bid Overview:</h4>
      <ul>
        ${comparisons.map(comp => `
          <li><strong>${comp.contractorName}:</strong> $${comp.bidAmount.toLocaleString()} - ${comp.timeline}</li>
        `).join('')}
      </ul>
      
      <p><em>Detailed analysis requires AI services to be properly configured.</em></p>
    </div>
  `;
}

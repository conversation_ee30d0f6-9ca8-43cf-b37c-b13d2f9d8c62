import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Contractor } from "@shared/schema";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronDown, ChevronRight, Save, User, Key, Plus, Eye, EyeOff, Copy, Check, Trash2, Edit, Calendar, Activity, Users, Shield, AlertTriangle, Clock, Bell } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { useUserRole } from "@/hooks/useUserRole";

// Form schema for the comprehensive contractor profile
const contractorFormSchema = z.object({
  // Business Identity & Contact
  companyName: z.string().min(1, "Company name is required"),
  companyWebsite: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  legalStructure: z.string().optional(),
  taxId: z.string().optional(),
  dba: z.string().optional(),
  primaryAddress: z.string().optional(),
  mailingAddress: z.string().optional(),
  primaryContactName: z.string().optional(),
  primaryContactEmail: z.string().email().optional().or(z.literal("")),
  primaryContactPhone: z.string().optional(),
  primaryContactTitle: z.string().optional(),
  
  // Classification & Capability
  tradeTypes: z.array(z.string()).min(1, "At least one trade type is required"),
  unionStatus: z.string().optional(),
  unionAffiliations: z.string().optional(),
  certifications: z.array(z.string()).optional(),
  serviceAreas: z.string().optional(),
  
  // Credentials & Compliance
  licenseNumber: z.string().optional(),
  licenseState: z.string().optional(),
  licenseExpiration: z.string().optional(),
  generalLiability: z.string().optional(),
  workersComp: z.string().optional(),
  autoInsurance: z.string().optional(),
  bondingSingle: z.number().optional(),
  bondingAggregate: z.number().optional(),
  emr: z.number().optional(),
  
  // Financial & Reference Data
  bankReference: z.string().optional(),
  suretyReference: z.string().optional(),
  creditRating: z.string().optional(),
  paymentTerms: z.string().optional(),
  litigationHistory: z.string().optional(),
  projectReferences: z.string().optional(),
  
  // Performance & Experience
  yearsInBusiness: z.number().optional(),
  specializations: z.array(z.string()).optional(),
  awards: z.string().optional(),
  environmentalPrograms: z.string().optional(),
  
  // Operational Details
  workforceSize: z.number().optional(),
  workforceBreakdown: z.string().optional(),
  equipment: z.string().optional(),
  availability: z.string().optional(),
  
  // Custom Tags & Preferences
  keywordTags: z.array(z.string()).optional(),
  preferredProjectTypes: z.array(z.string()).optional(),
});

type ContractorFormData = z.infer<typeof contractorFormSchema>;

// API Key form schema
const apiKeyFormSchema = z.object({
  name: z.string().min(1, "API key name is required"),
  permissions: z.enum(["read-only", "upload-only", "full-access"]),
  rateLimit: z.number().min(1).max(10000),
});

type ApiKeyFormData = z.infer<typeof apiKeyFormSchema>;

// Trade types options
const tradeOptions = [
  { value: "general_contractor", label: "General Contractor" },
  { value: "electrical", label: "Electrical" },
  { value: "plumbing", label: "Plumbing" },
  { value: "hvac", label: "HVAC" },
  { value: "concrete", label: "Concrete" },
  { value: "sitework", label: "Site Work/Excavation" },
  { value: "masonry", label: "Masonry" },
  { value: "structural_steel", label: "Structural Steel" },
  { value: "carpentry", label: "Carpentry" },
  { value: "roofing", label: "Roofing" },
  { value: "waterproofing", label: "Waterproofing" },
  { value: "insulation", label: "Insulation" },
  { value: "drywall", label: "Drywall" },
  { value: "flooring", label: "Flooring" },
  { value: "painting", label: "Painting" },
  { value: "fire_protection", label: "Fire Protection" },
  { value: "security_systems", label: "Security Systems" },
  { value: "landscaping", label: "Landscaping" },
  { value: "asphalt_paving", label: "Asphalt/Paving" },
  { value: "surveying", label: "Surveying" },
  { value: "environmental", label: "Environmental Services" },
  { value: "demolition", label: "Demolition" },
  { value: "utilities", label: "Utilities" },
  { value: "telecommunications", label: "Telecommunications" },
  { value: "glazing", label: "Glazing/Windows" },
  { value: "metal_fabrication", label: "Metal Fabrication" },
  { value: "elevator", label: "Elevator/Escalator" },
  { value: "architectural_millwork", label: "Architectural Millwork" },
];

// Certification options
const certificationOptions = [
  { value: "mbe", label: "MBE (Minority Business Enterprise)" },
  { value: "wbe", label: "WBE (Women Business Enterprise)" },
  { value: "vbe", label: "VBE (Veteran Business Enterprise)" },
  { value: "dbe", label: "DBE (Disadvantaged Business Enterprise)" },
  { value: "sbe", label: "SBE (Small Business Enterprise)" },
];

// Specialization options
const specializationOptions = [
  { value: "design_build", label: "Design/Build" },
  { value: "bim", label: "BIM" },
  { value: "prefab", label: "Prefabrication" },
  { value: "sustainable", label: "Sustainable Construction" },
  { value: "heavy_civil", label: "Heavy Civil" },
  { value: "emergency", label: "Emergency Work" },
];

// Project type options
const projectTypeOptions = [
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "infrastructure", label: "Infrastructure" },
  { value: "industrial", label: "Industrial" },
  { value: "institutional", label: "Institutional" },
  { value: "healthcare", label: "Healthcare" },
  { value: "education", label: "Education" },
];

export default function Settings() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [openSections, setOpenSections] = useState<string[]>(["business"]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedApiKey, setSelectedApiKey] = useState<any>(null);
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const [copiedKey, setCopiedKey] = useState<string | null>(null);

  // Fetch existing contractor profile
  const { data: contractor, isLoading } = useQuery<Contractor>({
    queryKey: ["/api/contractors/profile"],
    enabled: !!user,
  });

  // Fetch API keys
  const { data: apiKeys = [], isLoading: isLoadingApiKeys } = useQuery<any[]>({
    queryKey: ["/api/auth/api-keys"],
    enabled: !!user,
  });

  const form = useForm<ContractorFormData>({
    resolver: zodResolver(contractorFormSchema),
    defaultValues: {
      companyName: contractor?.companyName || "",
      companyWebsite: contractor?.companyWebsite || "",
      legalStructure: contractor?.legalStructure || "",
      taxId: contractor?.taxId || "",
      dba: contractor?.dba || "",
      primaryAddress: contractor?.primaryAddress || "",
      mailingAddress: contractor?.mailingAddress || "",
      primaryContactName: contractor?.primaryContactName || "",
      primaryContactEmail: contractor?.primaryContactEmail || "",
      primaryContactPhone: contractor?.primaryContactPhone || "",
      primaryContactTitle: contractor?.primaryContactTitle || "",
      tradeTypes: Array.isArray(contractor?.tradeTypes) ? contractor.tradeTypes : [],
      unionStatus: contractor?.unionStatus || "",
      unionAffiliations: contractor?.unionAffiliations || "",
      certifications: Array.isArray(contractor?.certifications) ? contractor.certifications : [],
      serviceAreas: contractor?.serviceAreas || "",
      licenseNumber: contractor?.licenseNumber || "",
      licenseState: contractor?.licenseState || "",
      licenseExpiration: contractor?.licenseExpiration ? new Date(contractor.licenseExpiration).toISOString().split('T')[0] : "",
      generalLiability: contractor?.generalLiability || "",
      workersComp: contractor?.workersComp || "",
      autoInsurance: contractor?.autoInsurance || "",
      bondingSingle: contractor?.bondingSingle || undefined,
      bondingAggregate: contractor?.bondingAggregate || undefined,
      emr: contractor?.emr ? Number(contractor.emr) : undefined,
      bankReference: contractor?.bankReference || "",
      suretyReference: contractor?.suretyReference || "",
      creditRating: contractor?.creditRating || "",
      paymentTerms: contractor?.paymentTerms || "",
      litigationHistory: contractor?.litigationHistory || "",
      projectReferences: contractor?.projectReferences || "",
      yearsInBusiness: contractor?.yearsInBusiness || undefined,
      specializations: Array.isArray(contractor?.specializations) ? contractor.specializations : [],
      awards: contractor?.awards || "",
      environmentalPrograms: contractor?.environmentalPrograms || "",
      workforceSize: contractor?.workforceSize || undefined,
      workforceBreakdown: contractor?.workforceBreakdown || "",
      equipment: contractor?.equipment || "",
      availability: contractor?.availability || "",
      keywordTags: Array.isArray(contractor?.keywordTags) ? contractor.keywordTags : [],
      preferredProjectTypes: Array.isArray(contractor?.preferredProjectTypes) ? contractor.preferredProjectTypes : [],
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (data: ContractorFormData) => {
      if (contractor?.id) {
        const response = await apiRequest("PATCH", `/api/contractors/${contractor.id}`, data);
        return response.json();
      } else {
        const response = await apiRequest("POST", "/api/contractors", data);
        return response.json();
      }
    },
    onSuccess: () => {
      toast({
        title: "Profile updated",
        description: "Your contractor profile has been saved successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/contractors/profile"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    },
  });

  // API Key form
  const apiKeyForm = useForm<ApiKeyFormData>({
    resolver: zodResolver(apiKeyFormSchema),
    defaultValues: {
      name: "",
      permissions: "read-only",
      rateLimit: 100,
    },
  });

  // Create API key mutation
  const createApiKeyMutation = useMutation({
    mutationFn: async (data: ApiKeyFormData) => {
      return await apiRequest("POST", "/api/auth/api-keys", data);
    },
    onSuccess: (response: any) => {
      toast({
        title: "API Key Created",
        description: "Your new API key has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/api-keys"] });
      setIsCreateDialogOpen(false);
      apiKeyForm.reset();
      // Show the full key to the user for the first time
      if (response && response.id) {
        setVisibleKeys(new Set([response.id]));
      }
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create API key. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Update API key mutation
  const updateApiKeyMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<ApiKeyFormData> }) => {
      return await apiRequest("PATCH", `/api/auth/api-keys/${id}`, data);
    },
    onSuccess: () => {
      toast({
        title: "API Key Updated",
        description: "API key has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/api-keys"] });
      setIsEditDialogOpen(false);
      setSelectedApiKey(null);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update API key. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete API key mutation
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (id: string) => {
      return await apiRequest("DELETE", `/api/auth/api-keys/${id}`);
    },
    onSuccess: () => {
      toast({
        title: "API Key Deleted",
        description: "API key has been deleted successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/api-keys"] });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete API key. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ContractorFormData) => {
    updateMutation.mutate(data);
  };

  const onApiKeySubmit = (data: ApiKeyFormData) => {
    createApiKeyMutation.mutate(data);
  };

  const toggleSection = (section: string) => {
    setOpenSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(keyId)) {
        newSet.delete(keyId);
      } else {
        newSet.add(keyId);
      }
      return newSet;
    });
  };

  const copyToClipboard = async (text: string, keyId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedKey(keyId);
      setTimeout(() => setCopiedKey(null), 2000);
      toast({
        title: "Copied",
        description: "API key copied to clipboard.",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy API key.",
        variant: "destructive",
      });
    }
  };

  const maskApiKey = (key: string) => {
    if (!key) return "";
    return `${key.substring(0, 8)}...${key.substring(key.length - 8)}`;
  };

  const openEditDialog = (apiKey: any) => {
    setSelectedApiKey(apiKey);
    apiKeyForm.setValue("name", apiKey.name);
    apiKeyForm.setValue("permissions", apiKey.permissions);
    apiKeyForm.setValue("rateLimit", apiKey.rateLimit);
    setIsEditDialogOpen(true);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-primary-foreground font-bold text-lg">B</span>
          </div>
          <p className="text-muted-foreground">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="flex items-center gap-3">
        <User className="h-6 w-6" />
        <h1 className="text-2xl font-semibold">Settings</h1>
      </div>
      
      <p className="text-muted-foreground">
        Manage your contractor profile and API keys for programmatic access.
      </p>

      {/* Organization Info Card */}
      {contractor && !isLoading && (
        <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Organization Connection
            </CardTitle>
            <CardDescription className="text-blue-700 dark:text-blue-300">
              Your contractor profile is linked to your organization for secure multi-tenant access
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Company Name</p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {contractor?.companyName || "Not specified"}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Organization ID</p>
                <p className="text-xs font-mono text-blue-600 dark:text-blue-400">
                  {contractor?.organizationId || "Not linked"}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Primary Trade</p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {(() => {
                    try {
                      if (contractor?.tradeTypes && Array.isArray(contractor.tradeTypes) && contractor.tradeTypes.length > 0) {
                        const firstTrade = contractor.tradeTypes[0];
                        const tradeOption = tradeOptions.find(opt => opt.value === firstTrade);
                        return tradeOption?.label || firstTrade || "Not specified";
                      }
                      return "Not specified";
                    } catch (error) {
                      console.warn("Error displaying trade types:", error);
                      return "Not specified";
                    }
                  })()}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Data Access Scope</p>
                <p className="text-sm text-blue-700 dark:text-blue-300">Organization-restricted</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className={`grid w-full ${(user as any)?.role === 'SuperUser' || (user as any)?.role === 'OrganizationAdmin' ? 'grid-cols-5' : 'grid-cols-3'}`}>
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Contractor Profile
          </TabsTrigger>
          <TabsTrigger value="api-keys" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            API Keys
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          {((user as any)?.role === 'SuperUser' || (user as any)?.role === 'OrganizationAdmin') && (
            <>
              <TabsTrigger value="user-management" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                User Management
              </TabsTrigger>
              <TabsTrigger value="audit-logs" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Audit Logs
              </TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="profile" className="space-y-6">

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          
          {/* Business Identity & Contact */}
          <Card>
            <Collapsible open={openSections.includes("business")} onOpenChange={() => toggleSection("business")}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50">
                  <CardTitle className="flex items-center justify-between">
                    <span>Business Identity & Contact</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted px-2 py-1 rounded">10 fields</span>
                      {openSections.includes("business") ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="companyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company Name *</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="legalStructure"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Legal Structure</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select structure" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="corporation">Corporation</SelectItem>
                            <SelectItem value="llc">LLC</SelectItem>
                            <SelectItem value="partnership">Partnership</SelectItem>
                            <SelectItem value="sole_proprietorship">Sole Proprietorship</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="taxId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax ID/EIN</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="dba"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>DBA (If applicable)</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="primaryAddress"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Primary Business Address</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="mailingAddress"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Mailing Address (if different)</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="primaryContactName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Contact Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="primaryContactEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Contact Email</FormLabel>
                        <FormControl>
                          <Input type="email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="primaryContactPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Contact Phone</FormLabel>
                        <FormControl>
                          <Input type="tel" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="primaryContactTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Contact Title</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Trade Types & Classification */}
          <Card>
            <Collapsible open={openSections.includes("classification")} onOpenChange={() => toggleSection("classification")}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50">
                  <CardTitle className="flex items-center justify-between">
                    <span>Classification & Capability</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted px-2 py-1 rounded">5 fields</span>
                      {openSections.includes("classification") ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="tradeTypes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Trade/Specialty Types *</FormLabel>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                          {tradeOptions.map((trade) => (
                            <div key={trade.value} className="flex items-center space-x-2">
                              <Checkbox
                                id={trade.value}
                                checked={field.value?.includes(trade.value) || false}
                                onCheckedChange={(checked) => {
                                  const currentValue = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentValue, trade.value]);
                                  } else {
                                    field.onChange(currentValue.filter((value) => value !== trade.value));
                                  }
                                }}
                              />
                              <Label htmlFor={trade.value} className="text-sm">{trade.label}</Label>
                            </div>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="unionStatus"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Union Status</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="union">Union</SelectItem>
                              <SelectItem value="non_union">Non-Union</SelectItem>
                              <SelectItem value="open_shop">Open Shop</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="unionAffiliations"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Union Affiliations</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="List specific local or national union affiliations" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="certifications"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Certification Designations</FormLabel>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                          {certificationOptions.map((cert) => (
                            <div key={cert.value} className="flex items-center space-x-2">
                              <Checkbox
                                id={cert.value}
                                checked={field.value?.includes(cert.value) || false}
                                onCheckedChange={(checked) => {
                                  const currentValue = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentValue, cert.value]);
                                  } else {
                                    field.onChange(currentValue.filter((value) => value !== cert.value));
                                  }
                                }}
                              />
                              <Label htmlFor={cert.value} className="text-sm">{cert.label}</Label>
                            </div>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="serviceAreas"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Service Areas</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            placeholder="List geographic coverage by Country, State, postal codes"
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Credentials & Compliance */}
          <Card>
            <Collapsible open={openSections.includes("credentials")} onOpenChange={() => toggleSection("credentials")}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50">
                  <CardTitle className="flex items-center justify-between">
                    <span>Credentials & Compliance</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted px-2 py-1 rounded">9 fields</span>
                      {openSections.includes("credentials") ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="licenseNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>License Number</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="licenseState"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>License State</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="licenseExpiration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>License Expiration</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="generalLiability"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>General Liability Insurance</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Carrier, policy number, coverage amount" rows={2} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="workersComp"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Workers' Compensation</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Carrier, policy number, coverage amount" rows={2} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="autoInsurance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Auto Insurance</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Carrier, policy number, coverage amount" rows={2} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="bondingSingle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bonding Capacity (Single)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field} 
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                            value={field.value || ""}
                            placeholder="Dollar amount"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="bondingAggregate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bonding Capacity (Aggregate)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field} 
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                            value={field.value || ""}
                            placeholder="Dollar amount"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="emr"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Experience Modification Rate (EMR)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field} 
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                            value={field.value || ""}
                            placeholder="e.g. 0.95"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Financial & Reference Data */}
          <Card>
            <Collapsible open={openSections.includes("financial")} onOpenChange={() => toggleSection("financial")}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50">
                  <CardTitle className="flex items-center justify-between">
                    <span>Financial & Reference Data</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted px-2 py-1 rounded">6 fields</span>
                      {openSections.includes("financial") ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="bankReference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Reference</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Bank name, contact, relationship" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="suretyReference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Surety Reference</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Surety company, contact, relationship" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="creditRating"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Credit Rating</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select rating" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="excellent">Excellent</SelectItem>
                            <SelectItem value="good">Good</SelectItem>
                            <SelectItem value="fair">Fair</SelectItem>
                            <SelectItem value="poor">Poor</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="paymentTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Terms</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="e.g. Net 30, 2/10 Net 30" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="litigationHistory"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Litigation History</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Describe any past or current litigation" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="projectReferences"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Project References</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="List significant projects with client contacts" rows={4} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Performance & Experience */}
          <Card>
            <Collapsible open={openSections.includes("performance")} onOpenChange={() => toggleSection("performance")}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50">
                  <CardTitle className="flex items-center justify-between">
                    <span>Performance & Experience</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted px-2 py-1 rounded">4 fields</span>
                      {openSections.includes("performance") ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="yearsInBusiness"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Years in Business</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              {...field} 
                              onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                              value={field.value || ""}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="specializations"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Specializations</FormLabel>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                          {specializationOptions.map((spec) => (
                            <div key={spec.value} className="flex items-center space-x-2">
                              <Checkbox
                                id={spec.value}
                                checked={field.value?.includes(spec.value) || false}
                                onCheckedChange={(checked) => {
                                  const currentValue = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentValue, spec.value]);
                                  } else {
                                    field.onChange(currentValue.filter((value) => value !== spec.value));
                                  }
                                }}
                              />
                              <Label htmlFor={spec.value} className="text-sm">{spec.label}</Label>
                            </div>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="awards"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Awards & Recognition</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="List any industry awards or recognition" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="environmentalPrograms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Environmental Programs</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Describe sustainability initiatives and programs" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Operational Details */}
          <Card>
            <Collapsible open={openSections.includes("operational")} onOpenChange={() => toggleSection("operational")}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50">
                  <CardTitle className="flex items-center justify-between">
                    <span>Operational Details</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted px-2 py-1 rounded">4 fields</span>
                      {openSections.includes("operational") ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="workforceSize"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Workforce Size</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field} 
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                            value={field.value || ""}
                            placeholder="Total number of employees"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="workforceBreakdown"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Workforce Breakdown</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Breakdown by trade/specialty" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="equipment"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Major Equipment</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="List major equipment and vehicles owned" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="availability"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Current Availability</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Describe current capacity and availability for new projects" rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Custom Tags & Preferences */}
          <Card>
            <Collapsible open={openSections.includes("preferences")} onOpenChange={() => toggleSection("preferences")}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50">
                  <CardTitle className="flex items-center justify-between">
                    <span>Custom Tags & Preferences</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted px-2 py-1 rounded">2 fields</span>
                      {openSections.includes("preferences") ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="keywordTags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Keyword Tags</FormLabel>
                        <FormControl>
                          <Input 
                            {...field} 
                            value={field.value?.join(", ") || ""}
                            onChange={(e) => field.onChange(e.target.value.split(",").map(tag => tag.trim()).filter(Boolean))}
                            placeholder="Enter keywords separated by commas"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="preferredProjectTypes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preferred Project Types</FormLabel>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                          {projectTypeOptions.map((type) => (
                            <div key={type.value} className="flex items-center space-x-2">
                              <Checkbox
                                id={type.value}
                                checked={field.value?.includes(type.value) || false}
                                onCheckedChange={(checked) => {
                                  const currentValue = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentValue, type.value]);
                                  } else {
                                    field.onChange(currentValue.filter((value) => value !== type.value));
                                  }
                                }}
                              />
                              <Label htmlFor={type.value} className="text-sm">{type.label}</Label>
                            </div>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={updateMutation.isPending}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {updateMutation.isPending ? "Saving..." : "Save Profile"}
            </Button>
          </div>
        </form>
      </Form>
        </TabsContent>

        <TabsContent value="api-keys" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">API Keys</h3>
              <p className="text-sm text-muted-foreground">
                Create and manage API keys for programmatic access to your Bidaible account.
              </p>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Create API Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New API Key</DialogTitle>
                  <DialogDescription>
                    Create a new API key for programmatic access. Choose the appropriate permissions for your use case.
                  </DialogDescription>
                </DialogHeader>
                <Form {...apiKeyForm}>
                  <form onSubmit={apiKeyForm.handleSubmit(onApiKeySubmit)} className="space-y-4">
                    <FormField
                      control={apiKeyForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="e.g., QuickBooks Integration" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={apiKeyForm.control}
                      name="permissions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Permissions</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="read-only">Read Only - View data only</SelectItem>
                              <SelectItem value="upload-only">Upload Only - Create RFQs and upload documents</SelectItem>
                              <SelectItem value="full-access">Full Access - Complete access to your account</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={apiKeyForm.control}
                      name="rateLimit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Rate Limit (requests per hour)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              {...field} 
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                              value={field.value}
                              min={1}
                              max={10000}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <DialogFooter>
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => setIsCreateDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={createApiKeyMutation.isPending}>
                        {createApiKeyMutation.isPending ? "Creating..." : "Create API Key"}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          {isLoadingApiKeys ? (
            <div className="flex items-center justify-center h-32">
              <p className="text-muted-foreground">Loading API keys...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {apiKeys.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center text-muted-foreground">
                      <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No API keys found</p>
                      <p className="text-sm">Create your first API key to get started with programmatic access.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                apiKeys.map((apiKey: any) => (
                  <Card key={apiKey.id}>
                    <CardContent className="pt-6">
                      <div className="flex items-start justify-between">
                        <div className="space-y-3 flex-1">
                          <div className="flex items-center gap-3">
                            <h4 className="font-semibold">{apiKey.name}</h4>
                            <Badge variant={apiKey.isActive ? "default" : "secondary"}>
                              {apiKey.isActive ? "Active" : "Inactive"}
                            </Badge>
                            <Badge variant="outline">{apiKey.permissions}</Badge>
                          </div>
                          
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                                {visibleKeys.has(apiKey.id) && apiKey.apiKey ? apiKey.apiKey : maskApiKey(apiKey.id)}
                              </code>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleKeyVisibility(apiKey.id)}
                              >
                                {visibleKeys.has(apiKey.id) ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                              {(visibleKeys.has(apiKey.id) && apiKey.apiKey) && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(apiKey.apiKey, apiKey.id)}
                                >
                                  {copiedKey === apiKey.id ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                                </Button>
                              )}
                            </div>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                Created: {formatDate(apiKey.createdAt)}
                              </div>
                              <div className="flex items-center gap-1">
                                <Activity className="h-3 w-3" />
                                Last used: {formatDate(apiKey.lastUsedAt)}
                              </div>
                              <div>
                                Rate limit: {apiKey.rateLimit}/hour
                              </div>
                              <div>
                                Expires: {formatDate(apiKey.expiresAt)}
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(apiKey)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete the API key "{apiKey.name}"? 
                                  This action cannot be undone and will immediately invalidate the key.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteApiKeyMutation.mutate(apiKey.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}

          {/* Edit API Key Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit API Key</DialogTitle>
                <DialogDescription>
                  Update the API key settings. Note that permissions cannot be changed after creation.
                </DialogDescription>
              </DialogHeader>
              <Form {...apiKeyForm}>
                <form onSubmit={apiKeyForm.handleSubmit((data) => {
                  if (selectedApiKey) {
                    updateApiKeyMutation.mutate({ id: selectedApiKey.id, data });
                  }
                })} className="space-y-4">
                  <FormField
                    control={apiKeyForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={apiKeyForm.control}
                    name="rateLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rate Limit (requests per hour)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field} 
                            onChange={(e) => field.onChange(parseInt(e.target.value))}
                            value={field.value}
                            min={1}
                            max={10000}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <DialogFooter>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setIsEditDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={updateApiKeyMutation.isPending}>
                      {updateApiKeyMutation.isPending ? "Updating..." : "Update API Key"}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <NotificationPreferencesSection />
        </TabsContent>

        {/* User Management Tab - Admin and SuperUser only */}
        {(user?.role === 'SuperUser' || user?.role === 'OrganizationAdmin') && (
          <TabsContent value="user-management" className="space-y-6">
            <UserManagementSection userRole={user?.role} />
          </TabsContent>
        )}

        {/* Audit Logs Tab - OrganizationAdmin and SuperUser only */}
        {(user?.role === 'SuperUser' || user?.role === 'OrganizationAdmin') && (
          <TabsContent value="audit-logs" className="space-y-6">
            <AuditLogsSection userRole={user?.role} />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}

// Notification Preferences Component
function NotificationPreferencesSection() {
  const { toast } = useToast();
  const { role } = useUserRole();
  const { user } = useAuth();

  // Define notification types based on role
  const getNotificationTypes = () => {
    if (role === 'contractor') {
      return [
        {
          category: 'Opportunity Alerts',
          icon: '🎯',
          notifications: [
            {
              type: 'rfq_available',
              title: 'New RFQ Available',
              description: 'When new RFQs match your trade types and service areas'
            },
            {
              type: 'rfq_updated',
              title: 'RFQ Updated',
              description: 'When changes are made to RFQs you\'re tracking'
            },
            {
              type: 'deadline_reminder',
              title: 'Deadline Reminder',
              description: '24 hours before RFQ submission deadline'
            }
          ]
        },
        {
          category: 'Bid Status Updates',
          icon: '📊',
          notifications: [
            {
              type: 'bid_accepted',
              title: 'Bid Accepted',
              description: 'When your bid is selected by the general contractor'
            },
            {
              type: 'bid_rejected',
              title: 'Bid Rejected',
              description: 'When your bid is not selected'
            },
            {
              type: 'request_for_info',
              title: 'Request for Info',
              description: 'When additional information is requested'
            }
          ]
        }
      ];
    } else {
      // General Contractor notifications
      return [
        {
          category: 'Bid Management',
          icon: '📋',
          notifications: [
            {
              type: 'bid_submitted',
              title: 'Bid Submitted',
              description: 'When contractors submit bids to your RFQs'
            },
            {
              type: 'bid_updated',
              title: 'Bid Updated',
              description: 'When contractors modify their bid submissions'
            },
            {
              type: 'deadline_approaching',
              title: 'Deadline Approaching',
              description: '24 hours before your RFQ deadline'
            }
          ]
        }
      ];
    }
  };

  // Global notification settings state
  const [globalSettings, setGlobalSettings] = useState({
    quietHours: false,
    weekendNotifications: true,
  });

  // Fetch current notification preferences
  const { data: preferences = [], isLoading } = useQuery({
    queryKey: ["/api/notifications/preferences"],
    enabled: !!user,
    staleTime: 0,
    gcTime: 0,
  });

  // Update preferences mutation
  const updatePreferencesMutation = useMutation({
    mutationFn: async ({ type, updates }: { type: string; updates: any }) => {
      return await apiRequest("PUT", `/api/notifications/preferences/${type}`, updates);
    },
    onMutate: async ({ type, updates }) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ["/api/notifications/preferences"] });

      // Snapshot the previous value
      const previousPreferences = queryClient.getQueryData(["/api/notifications/preferences"]);

      // Optimistically update to the new value
      queryClient.setQueryData(["/api/notifications/preferences"], (old: any) => {
        if (!old || !Array.isArray(old)) {
          return [{ ...updates, type }];
        }
        
        const existingIndex = old.findIndex((p: any) => p.type === type);
        if (existingIndex >= 0) {
          // Update existing preference
          const newPreferences = [...old];
          newPreferences[existingIndex] = { ...newPreferences[existingIndex], ...updates };
          return newPreferences;
        } else {
          // Add new preference
          return [...old, { ...updates, type }];
        }
      });

      // Return a context object with the snapshotted value
      return { previousPreferences };
    },
    onSuccess: () => {
      toast({
        title: "Preferences Updated",
        description: "Your notification preferences have been saved.",
      });
      // Don't update cache here - let optimistic update persist
    },
    onError: (err, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(["/api/notifications/preferences"], context?.previousPreferences);
      toast({
        title: "Error", 
        description: "Failed to update notification preferences.",
        variant: "destructive",
      });
    },
    onSettled: () => {
      // Refetch after a delay to sync with server, but let optimistic update show immediately
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["/api/notifications/preferences"] });
      }, 2000);
    },
  });

  // Get preference for a specific notification type
  const getPreference = (type: string) => {
    if (!preferences || !Array.isArray(preferences)) {
      return {
        type,
        inAppEnabled: true,
        emailEnabled: true,
        smsEnabled: false,
        frequency: 'immediate'
      };
    }
    
    const pref = preferences.find((p: any) => p.type === type);
    return pref || {
      type,
      inAppEnabled: true,
      emailEnabled: true,
      smsEnabled: false,
      frequency: 'immediate'
    };
  };

  const updatePreference = (type: string, field: string, value: any) => {
    const currentPref = getPreference(type);
    const updates = { 
      type,
      inAppEnabled: currentPref.inAppEnabled,
      emailEnabled: currentPref.emailEnabled,
      smsEnabled: currentPref.smsEnabled,
      frequency: currentPref.frequency,
      [field]: value 
    };
    updatePreferencesMutation.mutate({ type, updates });
  };

  const notificationTypes = getNotificationTypes();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-muted-foreground">Loading notification preferences...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Bell className="h-6 w-6" />
        <div>
          <h2 className="text-2xl font-semibold">Notification Preferences</h2>
          <p className="text-muted-foreground">
            Control how and when you receive notifications for different events.
          </p>
        </div>
      </div>

      {notificationTypes.map((category) => (
        <Card key={category.category}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>{category.icon}</span>
              {category.category}
            </CardTitle>
            <CardDescription>
              Manage notifications for {category.category.toLowerCase()} events
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {category.notifications.map((notification) => {
              const pref = getPreference(notification.type);
              
              return (
                <div key={notification.type} className="border rounded-lg p-4 space-y-4">
                  <div>
                    <h3 className="font-medium">{notification.title}</h3>
                    <p className="text-sm text-muted-foreground">{notification.description}</p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Delivery Methods */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Delivery Methods</Label>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Bell className="h-4 w-4" />
                          <span className="text-sm">In-App</span>
                        </div>
                        <Switch
                          checked={pref.inAppEnabled}
                          onCheckedChange={(checked) => 
                            updatePreference(notification.type, 'inAppEnabled', checked)
                          }
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">📧</span>
                          <span className="text-sm">Email</span>
                        </div>
                        <Switch
                          checked={pref.emailEnabled}
                          onCheckedChange={(checked) => 
                            updatePreference(notification.type, 'emailEnabled', checked)
                          }
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">📱</span>
                          <span className="text-sm">SMS</span>
                        </div>
                        <Switch
                          checked={pref.smsEnabled}
                          onCheckedChange={(checked) => 
                            updatePreference(notification.type, 'smsEnabled', checked)
                          }
                          disabled={true}
                        />
                      </div>
                      {pref.smsEnabled && (
                        <p className="text-xs text-muted-foreground">SMS coming soon</p>
                      )}
                    </div>

                    {/* Frequency Settings */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Frequency</Label>
                      <Select
                        value={pref.frequency}
                        onValueChange={(value) => 
                          updatePreference(notification.type, 'frequency', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="immediate">Immediate</SelectItem>
                          <SelectItem value="daily">Daily Digest</SelectItem>
                          <SelectItem value="weekly">Weekly Summary</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Priority Level */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Priority</Label>
                      <div className="flex items-center gap-2">
                        {pref.emailEnabled || pref.inAppEnabled ? (
                          <>
                            <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              Active
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {pref.frequency === 'immediate' ? 'Real-time' : `${pref.frequency} batch`}
                            </span>
                          </>
                        ) : (
                          <Badge variant="secondary">Disabled</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>
      ))}

      {/* Global Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>⚙️</span>
            Global Settings
          </CardTitle>
          <CardDescription>
            Apply settings across all notification types
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Quiet Hours</h3>
              <p className="text-sm text-muted-foreground">Disable notifications between 10 PM - 7 AM</p>
            </div>
            <Switch 
              checked={globalSettings.quietHours}
              onCheckedChange={(checked) => {
                setGlobalSettings(prev => ({ ...prev, quietHours: checked }));
                toast({
                  title: "Quiet Hours Updated",
                  description: checked ? "Notifications disabled 10 PM - 7 AM" : "Quiet hours disabled",
                });
              }}
            />
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Weekend Notifications</h3>
              <p className="text-sm text-muted-foreground">Receive notifications on weekends</p>
            </div>
            <Switch 
              checked={globalSettings.weekendNotifications} 
              onCheckedChange={(checked) => {
                setGlobalSettings(prev => ({ ...prev, weekendNotifications: checked }));
                toast({
                  title: "Weekend Notifications Updated",
                  description: checked ? "Weekend notifications enabled" : "Weekend notifications disabled",
                });
              }}
            />
          </div>

          {role !== 'contractor' && (
            <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <h3 className="font-medium text-blue-900 dark:text-blue-100">Tip for General Contractors</h3>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Enable bid submission notifications to stay informed when contractors respond to your RFQs. 
                Real-time notifications help you track engagement and respond quickly to questions.
              </p>
            </div>
          )}

          {role === 'contractor' && (
            <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg">
              <h3 className="font-medium text-green-900 dark:text-green-100">Tip for Contractors</h3>
              <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                Enable new RFQ notifications to be first to respond to opportunities. 
                Bid status notifications keep you informed about decisions on your submissions.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// User Management Component
function UserManagementSection({ userRole }: { userRole: string }) {
  const { toast } = useToast();
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);

  // Fetch users based on role
  const { data: users = [], isLoading } = useQuery<any[]>({
    queryKey: ["/api/admin/users"],
    enabled: userRole === 'SuperUser' || userRole === 'Admin',
  });

  // Role change form
  const roleForm = useForm({
    defaultValues: {
      role: "",
      reason: "",
    },
  });

  // Update user role mutation
  const updateRoleMutation = useMutation({
    mutationFn: async ({ userId, role, reason }: { userId: string, role: string, reason?: string }) => {
      return await apiRequest("PATCH", `/api/admin/users/${userId}/role`, { role, reason });
    },
    onSuccess: () => {
      toast({
        title: "Role Updated",
        description: "User role has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setIsRoleDialogOpen(false);
      roleForm.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update user role.",
        variant: "destructive",
      });
    },
  });

  const handleRoleChange = (data: { role: string; reason: string }) => {
    if (selectedUser) {
      updateRoleMutation.mutate({
        userId: selectedUser.id,
        role: data.role,
        reason: data.reason,
      });
    }
  };

  const openRoleDialog = (user: any) => {
    setSelectedUser(user);
    roleForm.setValue("role", user.role);
    setIsRoleDialogOpen(true);
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'SuperUser':
        return 'destructive';
      case 'Admin':
        return 'default';
      case 'Editor':
        return 'secondary';
      case 'Viewer':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-muted-foreground">Loading users...</p>
      </div>
    );
  }

  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">User Management</h3>
          <p className="text-sm text-muted-foreground">
            Manage user roles and permissions across your organization.
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 justify-between">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Users ({users?.length || 0})
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Badge variant={users?.length >= 15 ? "destructive" : "outline"}>
                {users?.length || 0}/15 users
              </Badge>
              {users?.length >= 15 && (
                <div className="flex items-center gap-1 text-red-500">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-xs">Limit reached</span>
                </div>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users?.map((user: any) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4" />
                      </div>
                      {user.name || `User ID: ${user.id}`}
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.isActive ? "default" : "secondary"}>
                      {user.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openRoleDialog(user)}
                      disabled={userRole !== 'SuperUser' && user.role === 'SuperUser'}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Role Change Dialog */}
      <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change User Role</DialogTitle>
            <DialogDescription>
              Update the role for {selectedUser?.name || selectedUser?.email}. This will affect their access permissions immediately.
            </DialogDescription>
          </DialogHeader>
          <Form {...roleForm}>
            <form onSubmit={roleForm.handleSubmit(handleRoleChange)} className="space-y-4">
              <FormField
                control={roleForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Role</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {userRole === 'SuperUser' && (
                          <SelectItem value="SuperUser">
                            <div className="flex items-center gap-2">
                              <Shield className="h-4 w-4 text-red-500" />
                              SuperUser - Full system access
                            </div>
                          </SelectItem>
                        )}
                        <SelectItem value="Admin">
                          <div className="flex items-center gap-2">
                            <Shield className="h-4 w-4 text-blue-500" />
                            Admin - Organization management
                          </div>
                        </SelectItem>
                        <SelectItem value="Editor">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-green-500" />
                            Editor - General access
                          </div>
                        </SelectItem>
                        <SelectItem value="Viewer">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-500" />
                            Viewer - Read-only access
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={roleForm.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason for Change (Optional)</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Explain why this role change is needed..." />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsRoleDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={updateRoleMutation.isPending}>
                  {updateRoleMutation.isPending ? "Updating..." : "Update Role"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

// Backfill Audit Button Component
function BackfillAuditButton() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const backfillMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/admin/audit/business/backfill', {});
      return await response.json();
    },
    onSuccess: (data) => {
      console.log('✅ Backfill successful:', data);
      toast({
        title: "Audit Backfill Complete",
        description: `Successfully created ${data.totalEntries} audit log entries from historical data.`,
      });
      // Refresh the business audit logs
      queryClient.invalidateQueries({ queryKey: ["/api/admin/audit/business"] });
    },
    onError: (error: any) => {
      console.error('❌ Backfill failed:', error);
      toast({
        title: "Backfill Failed",
        description: error.message || "Failed to backfill audit logs",
        variant: "destructive",
      });
    },
  });

  const handleBackfill = () => {
    setIsLoading(true);
    backfillMutation.mutate();
    setTimeout(() => setIsLoading(false), 3000); // Reset loading after 3 seconds
  };

  return (
    <Button
      onClick={handleBackfill}
      disabled={isLoading || backfillMutation.isPending}
      size="sm"
      variant="outline"
      className="flex items-center gap-2"
    >
      {isLoading ? (
        <>
          <Clock className="h-4 w-4 animate-spin" />
          Backfilling...
        </>
      ) : (
        <>
          <Activity className="h-4 w-4" />
          Backfill Historical Data
        </>
      )}
    </Button>
  );
}

// Audit Logs Component
function AuditLogsSection({ userRole }: { userRole: string }) {
  const [auditType, setAuditType] = useState<'roles' | 'access' | 'business'>('roles');

  // Fetch audit logs
  const { data: roleAuditLogs, isLoading: isLoadingRoleLogs } = useQuery({
    queryKey: ["/api/admin/audit/roles"],
    enabled: userRole === 'SuperUser' || userRole === 'OrganizationAdmin',
  });

  const { data: accessAuditLogs, isLoading: isLoadingAccessLogs } = useQuery({
    queryKey: ["/api/admin/audit/access"],
    enabled: userRole === 'SuperUser' || userRole === 'OrganizationAdmin',
  });

  const { data: businessAuditLogs, isLoading: isLoadingBusinessLogs } = useQuery({
    queryKey: ["/api/admin/audit/business"],
    enabled: userRole === 'SuperUser' || userRole === 'OrganizationAdmin',
  });

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Audit Logs</h3>
          <p className="text-sm text-muted-foreground">
            Monitor system access and role changes for security compliance.
          </p>
        </div>
      </div>

      <Tabs value={auditType} onValueChange={(value) => setAuditType(value as 'roles' | 'access' | 'business')}>
        <TabsList>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Role Changes
          </TabsTrigger>
          <TabsTrigger value="access" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Access Attempts
          </TabsTrigger>
          <TabsTrigger value="business" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Business Events
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Role Change History
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingRoleLogs ? (
                <div className="flex items-center justify-center h-32">
                  <p className="text-muted-foreground">Loading audit logs...</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Target User</TableHead>
                      <TableHead>Changed By</TableHead>
                      <TableHead>Previous Role</TableHead>
                      <TableHead>New Role</TableHead>
                      <TableHead>Reason</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roleAuditLogs?.map((log: any) => (
                      <TableRow key={log.id}>
                        <TableCell>{formatTimestamp(log.createdAt)}</TableCell>
                        <TableCell>{log.targetUserId}</TableCell>
                        <TableCell>{log.changedByUserId}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{log.previousRole}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="default">{log.newRole}</Badge>
                        </TableCell>
                        <TableCell>{log.reason || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="access" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Access Audit Trail
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingAccessLogs ? (
                <div className="flex items-center justify-center h-32">
                  <p className="text-muted-foreground">Loading access logs...</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Resource</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Reason</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {accessAuditLogs?.slice(0, 50).map((log: any) => (
                      <TableRow key={log.id}>
                        <TableCell>{formatTimestamp(log.createdAt)}</TableCell>
                        <TableCell>{log.userId}</TableCell>
                        <TableCell><code className="text-sm">{log.action}</code></TableCell>
                        <TableCell><code className="text-sm">{log.resource}</code></TableCell>
                        <TableCell>
                          <Badge variant={log.success ? "default" : "destructive"}>
                            {log.success ? "Success" : "Failed"}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {log.reason || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Business Events Audit Trail
                </div>
                <BackfillAuditButton />
              </CardTitle>
              <CardDescription>
                Track RFQ creation, bid submissions, file uploads, distribution events, and email notifications for compliance.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingBusinessLogs ? (
                <div className="flex items-center justify-center h-32">
                  <p className="text-muted-foreground">Loading business audit logs...</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Event Type</TableHead>
                      <TableHead>Resource</TableHead>
                      <TableHead>Details</TableHead>
                      <TableHead>IP Address</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(businessAuditLogs || []).slice(0, 50).map((log: any) => (
                      <TableRow key={log.id}>
                        <TableCell>{formatTimestamp(log.createdAt)}</TableCell>
                        <TableCell>{log.userId}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {log.eventType.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {log.resourceId || '-'}
                        </TableCell>
                        <TableCell className="max-w-md">
                          <div className="text-sm">
                            {log.eventType === 'file_upload' && log.eventData?.fileName && (
                              <span>📄 {log.eventData.fileName} ({(log.eventData.fileSize / 1024).toFixed(1)}KB)</span>
                            )}
                            {log.eventType === 'rfq_creation' && log.eventData?.projectName && (
                              <span>🏗️ {log.eventData.projectName}</span>
                            )}
                            {log.eventType === 'bid_action' && log.eventData?.action && (
                              <span>📝 {log.eventData.action} - ${log.eventData.bidAmount?.toLocaleString() || 'TBD'}</span>
                            )}
                            {log.eventType === 'rfq_distribution' && log.eventData?.contractorCount && (
                              <span>📬 Distributed to {log.eventData.contractorCount} contractors via {log.eventData.distributionMethod}</span>
                            )}
                            {log.eventType.startsWith('email_') && log.eventData?.emailAddress && (
                              <span>
                                📧 {log.eventData.subject} → <strong>{log.eventData.emailAddress}</strong>
                                {log.eventData.messageId && (
                                  <span className="text-xs text-muted-foreground ml-2">
                                    (ID: {log.eventData.messageId.substring(0, 8)}...)
                                  </span>
                                )}
                                {log.eventData.errorMessage && (
                                  <span className="text-xs text-red-600 ml-2">
                                    Error: {log.eventData.errorMessage}
                                  </span>
                                )}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-xs text-muted-foreground">
                          {log.ipAddress || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </>
  );
}
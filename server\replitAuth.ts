import * as client from "openid-client";
import { Strategy, type VerifyFunction } from "openid-client/passport";

import passport from "passport";
import session from "express-session";
import type { Express, RequestHandler } from "express";
import memoize from "memoizee";
import connectPg from "connect-pg-simple";
import { storage } from "./storage";

if (!process.env.REPLIT_DOMAINS) {
  throw new Error("Environment variable REPLIT_DOMAINS not provided");
}

const getOidcConfig = memoize(
  async () => {
    return await client.discovery(
      new URL(process.env.ISSUER_URL ?? "https://replit.com/oidc"),
      process.env.REPL_ID!
    );
  },
  { maxAge: 3600 * 1000 }
);

export function getSession() {
  const sessionTtl = 72 * 60 * 60 * 1000; // 72 hours (3 days)
  const pgStore = connectPg(session);
  const sessionStore = new pgStore({
    conString: process.env.DATABASE_URL,
    createTableIfMissing: false,
    ttl: sessionTtl,
    tableName: "sessions",
  });
  return session({
    secret: process.env.SESSION_SECRET!,
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    rolling: true, // Enable sliding session expiration
    cookie: {
      httpOnly: true,
      secure: true,
      maxAge: sessionTtl,
      sameSite: 'lax', // Allow OAuth redirects to work
    },
  });
}

function updateUserSession(
  user: any,
  tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers
) {
  user.claims = tokens.claims();
  user.access_token = tokens.access_token;
  user.refresh_token = tokens.refresh_token;
  user.expires_at = user.claims?.exp;
  user.lastActivity = Date.now(); // Track last activity for security
}

// Enhanced session management functions
export async function invalidateUserSessions(userId: string): Promise<boolean> {
  try {
    // Get the session store
    const pgStore = connectPg(session);
    const sessionStore = new pgStore({
      conString: process.env.DATABASE_URL,
      createTableIfMissing: false,
      tableName: "sessions",
    });

    // Query to find and destroy sessions for a specific user
    return new Promise((resolve, reject) => {
      // Note: This is a simplified approach - in production you'd want a more robust way
      // to track user sessions, potentially using a custom session store or additional tracking
      sessionStore.all((err: any, sessions: any) => {
        if (err) {
          console.error('Error fetching sessions for invalidation:', err);
          return reject(err);
        }

        let invalidated = 0;
        const sessionIds = Object.keys(sessions || {});
        
        sessionIds.forEach(sessionId => {
          const sessionData = sessions[sessionId];
          if (sessionData?.passport?.user?.claims?.sub === userId) {
            sessionStore.destroy(sessionId, (destroyErr: any) => {
              if (destroyErr) {
                console.error('Error destroying session:', destroyErr);
              } else {
                invalidated++;
              }
            });
          }
        });

        console.log(`Invalidated ${invalidated} sessions for user ${userId}`);
        resolve(invalidated > 0);
      });
    });
  } catch (error) {
    console.error('Failed to invalidate user sessions:', error);
    return false;
  }
}

export function regenerateSession(req: any): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!req.session) {
      return resolve();
    }

    req.session.regenerate((err: any) => {
      if (err) {
        console.error('Failed to regenerate session:', err);
        return reject(err);
      }
      resolve();
    });
  });
}

async function upsertUser(
  claims: any,
) {
  await storage.upsertUser({
    id: claims["sub"],
    email: claims["email"],
    firstName: claims["first_name"],
    lastName: claims["last_name"],
    profileImageUrl: claims["profile_image_url"],
  });
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  app.use(getSession());
  app.use(passport.initialize());
  app.use(passport.session());

  const config = await getOidcConfig();

  const verify: VerifyFunction = async (
    tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers,
    verified: passport.AuthenticateCallback
  ) => {
    const user = {};
    updateUserSession(user, tokens);
    await upsertUser(tokens.claims());
    verified(null, user);
  };

  for (const domain of process.env
    .REPLIT_DOMAINS!.split(",")) {
    const strategy = new Strategy(
      {
        name: `replitauth:${domain}`,
        config,
        scope: "openid email profile offline_access",
        callbackURL: `https://${domain}/api/callback`,
      },
      verify,
    );
    passport.use(strategy);
  }

  passport.serializeUser((user: Express.User, cb) => cb(null, user));
  passport.deserializeUser((user: Express.User, cb) => cb(null, user));

  app.get("/api/login", (req, res, next) => {
    passport.authenticate(`replitauth:${req.hostname}`, {
      prompt: "login consent",
      scope: ["openid", "email", "profile", "offline_access"],
    })(req, res, next);
  });

  app.get("/api/callback", (req, res, next) => {
    passport.authenticate(`replitauth:${req.hostname}`, {
      successReturnToOrRedirect: "/",
      failureRedirect: "/api/login",
    })(req, res, next);
  });

  app.get("/api/logout", (req, res) => {
    req.logout(() => {
      res.redirect(
        client.buildEndSessionUrl(config, {
          client_id: process.env.REPL_ID!,
          post_logout_redirect_uri: `${req.protocol}://${req.hostname}`,
        }).href
      );
    });
  });
}

export const isAuthenticated: RequestHandler = async (req, res, next) => {
  const user = req.user as any;

  if (!req.isAuthenticated() || !user.expires_at) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  const now = Math.floor(Date.now() / 1000);
  if (now <= user.expires_at) {
    // Session is valid, update last activity time for sliding expiration
    if (req.session) {
      req.session.touch();
    }
    return next();
  }

  const refreshToken = user.refresh_token;
  if (!refreshToken) {
    // Clear invalid session
    req.session?.destroy((err) => {
      if (err) console.error('Failed to destroy session:', err);
    });
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  try {
    const config = await getOidcConfig();
    const tokenResponse = await client.refreshTokenGrant(config, refreshToken);
    updateUserSession(user, tokenResponse);
    
    // Regenerate session ID for security after token refresh
    req.session?.regenerate((err) => {
      if (err) {
        console.error('Failed to regenerate session:', err);
        return res.status(500).json({ message: "Session error" });
      }
      return next();
    });
  } catch (error) {
    // Clear invalid session
    req.session?.destroy((err) => {
      if (err) console.error('Failed to destroy session:', err);
    });
    res.status(401).json({ message: "Unauthorized" });
    return;
  }
};

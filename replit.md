# Bidaible.com - Construction RFQ Platform

## Overview
Bidaible is a comprehensive construction RFQ (Request for Quote) and bidding platform designed to streamline project management between general contractors and subcontractors. It features AI-powered document processing using Google Gemini to extract key data from RFQ documents. The platform aims to provide deep bid analysis, including competitive scoring, risk assessment, and strategic recommendations, significantly enhancing efficiency for all users. The business vision is to deliver a "wow factor" through these advanced capabilities, creating market potential by simplifying and optimizing the construction bidding process.

## User Preferences
Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter
- **Styling**: Tailwind CSS with shadcn/ui
- **State Management**: TanStack Query
- **Build Tool**: Vite
- **UI/UX Decisions**: Mobile-first responsive design; Custom light/dark theme with Plus Jakarta Sans typography; Warm beige/dark brown color scheme with orange accents.

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **API Design**: RESTful API with structured error handling
- **File Upload**: Multer middleware
- **Session Management**: Express sessions with PostgreSQL store

### Database Architecture
- **Database**: PostgreSQL (Neon serverless)
- **ORM**: Drizzle ORM with type-safe queries
- **Schema**: Comprehensive relational schema supporting users, contractors, RFQs, bids, documents, API keys, organizations, and audit logs.
- **Multi-Tenant Support**: Complete organizational isolation with role-based data access.
- **Security Architecture**: Advanced audit logging.
- **Migrations**: Drizzle Kit for schema management.
- **Performance**: Strategic database indexes.
- **Caching**: In-memory caching with TTL and LRU eviction policies.
- **Compliance**: Comprehensive audit trails.

### Core System Features
- **Authentication & Authorization**: Replit Auth (OpenID Connect) with PostgreSQL-backed sessions; 4-tier role-based access control (SuperUser, Admin, Editor, Viewer) with organization-scoped access; JWT-based API key authentication with scoped permissions and rate limiting.
- **Document Processing**: AI-powered extraction (Google Gemini/OpenAI) from PDF, TXT, CSV (up to 250MB); Multi-file RFQ processing with priority-based AI analysis; 6-stage progress tracking with ETA.
- **Core Business Logic**: RFQ lifecycle management; Contractor profiles and management; Smart RFQ distribution; Bid submission, tracking, and analysis; Dashboard analytics; Comprehensive business audit logging; ERP/CRM integration with cost code synchronization.
- **User Interface**: Responsive design; Interactive data tables; Real-time updates; Role-based dynamic sidebar navigation; Enhanced bid submission UX.
- **Terms & Conditions Management**: Professional terms modal with user acceptance tracking, database audit trails, and automatic grandfathering.
- **Notifications**: Real-time notification system (email, in-app, SMS options) with user-configurable preferences.
- **Export & Reporting**: Professional-grade export to CSV and PDF with advanced analytics reports (Chart.js visualizations) and email reporting framework.
- **Security Hardening**: Implementation of security headers, CORS configuration, input sanitization, rate limiting, enhanced session security, and API key management with rotation and lockout features.

## External Dependencies
- **@neondatabase/serverless**: PostgreSQL connection
- **@google/genai**: Google Gemini AI
- **@replit/object-storage**: Secure file storage
- **@tanstack/react-query**: Server state management
- **drizzle-orm**: ORM
- **passport**: Authentication middleware
- **multer**: File upload handling
- **pdfjs-dist**: PDF text extraction
- **jsonwebtoken**: JWT token handling
- **Resend**: Email service integration
- **Groq/moonshotai/kimi-k2-instruct**: Primary AI model
- **OpenAI**: Fallback AI model
- **@radix-ui/***: Headless UI components
- **tailwindcss**: CSS framework
- **react-hook-form**: Form state management
- **zod**: Validation
- **vite**: Build tool
- **typescript**: Type checking
- **tsx**: TypeScript execution
- **Chart.js**: Analytics visualizations
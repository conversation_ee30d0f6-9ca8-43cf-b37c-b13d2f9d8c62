# Bidaible.com - Construction RFQ Platform

## Overview
Bidaible is a comprehensive construction RFQ (Request for Quote) and bidding platform designed to streamline project management between general contractors and subcontractors. It features AI-powered document processing using Google Gemini to extract key data from RFQ documents. The platform aims to provide deep bid analysis, including competitive scoring, risk assessment, and strategic recommendations, significantly enhancing efficiency for all users. The business vision is to deliver a "wow factor" through these advanced capabilities, creating market potential by simplifying and optimizing the construction bidding process.

## User Preferences
Preferred communication style: Simple, everyday language.

## Recent Changes & Issues
**Date**: August 7, 2025
**FULLY RESOLVED**: Contractor classification sidebar logic fixed - Simplified role system
- **Previous Issue**: Classification settings in Settings page weren't affecting sidebar navigation correctly
- **Root Cause**: Complex SuperUser role logic with duplicate classification checks causing interface mode confusion
- **SOLUTION IMPLEMENTED**: Simplified role system to use only General Contractor vs Contractor classification
- **Architecture Changes**: 
  - Removed SuperUser role complexity from useUserRole.ts
  - Eliminated duplicate getInterfaceMode() functions
  - Streamlined sidebar logic to directly check role instead of interfaceMode
- **Logic Flow**: tradeTypes contains "General Contractor" → general_contractor role → GC sidebar, otherwise contractor role → Contractor sidebar
- **Files Modified**: client/src/hooks/useUserRole.ts, client/src/components/Sidebar.tsx
- **User Confirmation**: "Yes, it works well now" - Classification changes in Settings now properly update sidebar navigation
- **Status**: ✅ PRODUCTION READY - Classification system working as expected

**Date**: August 6, 2025
**FULLY RESOLVED**: PDF text extraction regression - Complete system integration
- **Previous Issue**: PDF processing only extracted 29 characters instead of actual content
- **SOLUTION IMPLEMENTED**: Created and integrated unified PDF extraction service across all processing paths
- **Test Results**: Successfully extracted 24,506 characters from 88-page RFQ document in 3 seconds
- **Architecture**: Enhanced PDF.js extraction with fallback support (pdf-parse, Gemini Vision)
- **Performance**: 70% confidence score, robust text item processing with custom font encoding support
- **FINAL INTEGRATION**: ✅ COMPLETE - Replaced all broken PDF processing paths
  - ✅ Fixed aiStreamProcessor.ts (stream processing path)
  - ✅ Fixed aiService.ts extractBidPdfWithGroq function (bid processing path)
  - ✅ User confirmed: "FINALLY! That fixed it" - system working perfectly
- **Live Test**: Bid processing now extracts real content (e.g., $37,443 bid amount from Kaser Painting PDF)
- **Files Modified**: server/services/core/pdfExtractor.ts, server/services/aiStreamProcessor.ts, server/services/aiService.ts
- **Status**: ✅ PRODUCTION READY - All PDF processing now uses unified extractor

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter
- **Styling**: Tailwind CSS with shadcn/ui
- **State Management**: TanStack Query
- **Build Tool**: Vite
- **UI/UX Decisions**: Mobile-first responsive design; Custom light/dark theme with Plus Jakarta Sans typography; Warm beige/dark brown color scheme with orange accents.

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **API Design**: RESTful API with structured error handling
- **File Upload**: Multer middleware
- **Session Management**: Express sessions with PostgreSQL store

### Database Architecture
- **Database**: PostgreSQL (Neon serverless)
- **ORM**: Drizzle ORM with type-safe queries
- **Schema**: Comprehensive relational schema supporting users, contractors, RFQs, bids, documents, API keys, organizations, and audit logs.
- **Multi-Tenant Support**: Complete organizational isolation with role-based data access.
- **Security Architecture**: Advanced audit logging.
- **Migrations**: Drizzle Kit for schema management.
- **Performance**: Strategic database indexes.
- **Caching**: In-memory caching with TTL and LRU eviction policies.
- **Compliance**: Comprehensive audit trails.

### Core System Features
- **Authentication & Authorization**: Replit Auth (OpenID Connect) with PostgreSQL-backed sessions; Simplified 2-tier role-based access control (General Contractor, Contractor) based on tradeTypes classification; JWT-based API key authentication with scoped permissions and rate limiting.
- **Document Processing**: AI-powered extraction (Google Gemini/OpenAI) from PDF, TXT, CSV (up to 250MB); Multi-file RFQ processing with priority-based AI analysis; 6-stage progress tracking with ETA; Robust PDF text extraction handling multiple PDF.js content structures.
- **Core Business Logic**: RFQ lifecycle management; Contractor profiles and management; Smart RFQ distribution; Bid submission, tracking, and analysis; Dashboard analytics; Comprehensive business audit logging; ERP/CRM integration with cost code synchronization.
- **User Interface**: Responsive design; Interactive data tables; Real-time updates; Role-based dynamic sidebar navigation; Enhanced bid submission UX.
- **Terms & Conditions Management**: Professional terms modal with user acceptance tracking, database audit trails, and automatic grandfathering.
- **Notifications**: Real-time notification system (email, in-app, SMS options) with user-configurable preferences.
- **Export & Reporting**: Professional-grade export to CSV and PDF with advanced analytics reports (Chart.js visualizations) and email reporting framework.
- **Security Hardening**: Implementation of security headers, CORS configuration, input sanitization, rate limiting, enhanced session security, and API key management with rotation and lockout features.

## External Dependencies
- **@neondatabase/serverless**: PostgreSQL connection
- **@google/genai**: Google Gemini AI
- **@replit/object-storage**: Secure file storage
- **@tanstack/react-query**: Server state management
- **drizzle-orm**: ORM
- **passport**: Authentication middleware
- **multer**: File upload handling
- **pdfjs-dist**: PDF text extraction
- **jsonwebtoken**: JWT token handling
- **Resend**: Email service integration
- **Groq/moonshotai/kimi-k2-instruct**: Primary AI model
- **OpenAI**: Fallback AI model
- **@radix-ui/***: Headless UI components
- **tailwindcss**: CSS framework
- **react-hook-form**: Form state management
- **zod**: Validation
- **vite**: Build tool
- **typescript**: Type checking
- **tsx**: TypeScript execution
- **Chart.js**: Analytics visualizations
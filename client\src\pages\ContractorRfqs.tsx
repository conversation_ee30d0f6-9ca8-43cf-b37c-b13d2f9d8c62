import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { FileText, Search, Filter, Eye, Calendar, MapPin, Clock, X, Send, AlertCircle } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { BidSubmissionForm } from "@/components/BidSubmissionForm";

interface RfqDistribution {
  id: string;
  rfqId: string;
  sentAt: string;
  viewedAt: string | null;
  declinedAt: string | null;
  declineReason: string | null;
  rfq: {
    id: string;
    projectName: string;
    projectLocation: string;
    description: string | null;
    tradeCategory: string;
    dueDate: string;
    status: string;
    extractedData: any;
    createdAt: string;
  };
}

export default function ContractorRfqs() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedRfq, setSelectedRfq] = useState<RfqDistribution | null>(null);
  const [showBidForm, setShowBidForm] = useState(false);
  const [showDeclineDialog, setShowDeclineDialog] = useState(false);
  const [declineReason, setDeclineReason] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all active RFQs available for bidding
  const { data: rfqs = [], isLoading } = useQuery<RfqDistribution[]>({
    queryKey: ['/api/contractors/rfqs/all'],
  });

  // Mark RFQ as viewed
  const markViewedMutation = useMutation({
    mutationFn: (rfqId: string) => apiRequest('PATCH', `/api/rfqs/${rfqId}/view`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contractors/rfqs'] });
    }
  });

  // Decline RFQ mutation
  const declineMutation = useMutation({
    mutationFn: ({ rfqId, reason }: { rfqId: string; reason: string }) => 
      apiRequest('PATCH', `/api/rfqs/${rfqId}/decline`, { reason }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contractors/rfqs'] });
      setShowDeclineDialog(false);
      setDeclineReason("");
      toast({ title: "RFQ declined successfully" });
    },
    onError: () => {
      toast({ title: "Failed to decline RFQ", variant: "destructive" });
    }
  });

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "draft": return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
      case "active": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "review": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "closed": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "awarded": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const getTradeColor = (trade: string) => {
    const colors = {
      electrical: "border-blue-200 text-blue-700 bg-blue-50",
      plumbing: "border-green-200 text-green-700 bg-green-50",
      hvac: "border-purple-200 text-purple-700 bg-purple-50",
      concrete: "border-gray-200 text-gray-700 bg-gray-50",
      general: "border-orange-200 text-orange-700 bg-orange-50",
      site_work: "border-yellow-200 text-yellow-700 bg-yellow-50",
    };
    return colors[trade as keyof typeof colors] || "border-gray-200 text-gray-700 bg-gray-50";
  };

  const handleViewRfq = (rfqDist: RfqDistribution) => {
    setSelectedRfq(rfqDist);
    if (!rfqDist.viewedAt) {
      markViewedMutation.mutate(rfqDist.rfqId);
    }
  };

  const handleSubmitBid = (rfqDist: RfqDistribution) => {
    setSelectedRfq(rfqDist);
    setShowBidForm(true);
    if (!rfqDist.viewedAt) {
      markViewedMutation.mutate(rfqDist.rfqId);
    }
  };

  const handleDeclineRfq = (rfqDist: RfqDistribution) => {
    setSelectedRfq(rfqDist);
    setShowDeclineDialog(true);
  };

  const filteredRfqs = rfqs.filter(rfqDist => {
    const rfq = rfqDist.rfq;
    const matchesSearch = rfq.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rfq.projectLocation.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || 
                         (statusFilter === "new" && !rfqDist.viewedAt) ||
                         (statusFilter === "viewed" && rfqDist.viewedAt && !rfqDist.declinedAt) ||
                         (statusFilter === "declined" && rfqDist.declinedAt);
    
    return matchesSearch && matchesStatus;
  });

  const isOverdue = (dueDate: string) => new Date(dueDate) < new Date();

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-64" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="h-64 bg-muted rounded-lg" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">RFQs for Bid</h1>
          <p className="text-muted-foreground">Browse and respond to active RFQs available for bidding</p>
        </div>
        <Badge variant="outline" className="text-lg px-4 py-2">
          {rfqs.filter(r => !r.declinedAt).length} Active
        </Badge>
      </div>

      {/* Filters */}
      <div className="flex gap-4 mb-6">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All RFQs</SelectItem>
            <SelectItem value="new">New (Unviewed)</SelectItem>
            <SelectItem value="viewed">Viewed</SelectItem>
            <SelectItem value="declined">Declined</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* RFQ Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRfqs.map((rfqDist) => {
          const rfq = rfqDist.rfq;
          const isNew = !rfqDist.viewedAt;
          const isDeclined = !!rfqDist.declinedAt;
          const overdue = isOverdue(rfq.dueDate);

          return (
            <Card key={rfqDist.id} className={`relative hover:shadow-lg transition-shadow ${
              isNew ? 'ring-2 ring-blue-500' : ''
            } ${isDeclined ? 'opacity-60' : ''}`}>
              {isNew && (
                <Badge className="absolute -top-2 -right-2 bg-blue-500">New</Badge>
              )}
              {isDeclined && (
                <Badge className="absolute -top-2 -right-2 bg-red-500">Declined</Badge>
              )}
              
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg truncate pr-2">{rfq.projectName}</CardTitle>
                  <Badge className={getStatusColor(rfq.status)}>
                    {rfq.status}
                  </Badge>
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span className="truncate">{rfq.projectLocation}</span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className={getTradeColor(rfq.tradeCategory)}>
                    {rfq.tradeCategory}
                  </Badge>
                  <div className={`flex items-center text-sm ${overdue ? 'text-red-600' : 'text-muted-foreground'}`}>
                    <Clock className="h-3 w-3 mr-1" />
                    <span>Due {format(new Date(rfq.dueDate), 'MMM dd')}</span>
                    {overdue && <AlertCircle className="h-3 w-3 ml-1" />}
                  </div>
                </div>
                
                {rfq.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {rfq.description}
                  </p>
                )}

                <div className="text-xs text-muted-foreground">
                  Received {formatDistanceToNow(new Date(rfqDist.sentAt), { addSuffix: true })}
                </div>
                
                {!isDeclined && (
                  <div className="flex gap-2 pt-2">
                    {/* Primary Action - Submit Bid */}
                    <Button 
                      size="sm"
                      onClick={() => handleSubmitBid(rfqDist)}
                      className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
                      disabled={overdue}
                    >
                      <Send className="mr-2 h-4 w-4" />
                      {overdue ? 'Deadline Passed' : 'Submit Bid'}
                    </Button>
                    
                    {/* Secondary Actions */}
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewRfq(rfqDist)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDeclineRfq(rfqDist)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}

                {isDeclined && rfqDist.declineReason && (
                  <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
                    <strong>Declined:</strong> {rfqDist.declineReason}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredRfqs.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No RFQs found</h3>
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== "all" 
                ? "Try adjusting your filters"
                : "No RFQs have been sent to your company yet"
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Decline Dialog */}
      <Dialog open={showDeclineDialog} onOpenChange={setShowDeclineDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Decline RFQ</DialogTitle>
            <DialogDescription>
              Please provide a reason for declining this RFQ opportunity.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              placeholder="Reason for declining (e.g., outside service area, schedule conflict, not our specialty...)"
              value={declineReason}
              onChange={(e) => setDeclineReason(e.target.value)}
              rows={3}
            />
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setShowDeclineDialog(false)}>
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={() => selectedRfq && declineMutation.mutate({ 
                  rfqId: selectedRfq.rfqId, 
                  reason: declineReason 
                })}
                disabled={!declineReason.trim() || declineMutation.isPending}
              >
                Decline RFQ
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* RFQ Detail View Modal */}
      {selectedRfq && !showBidForm && (
        <Dialog open={!!selectedRfq} onOpenChange={() => setSelectedRfq(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {selectedRfq.rfq.projectName}
              </DialogTitle>
              <DialogDescription>
                RFQ Details and Project Information
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              {/* Project Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Project Location
                  </h4>
                  <p className="text-muted-foreground">{selectedRfq.rfq.projectLocation}</p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Due Date
                  </h4>
                  <p className="text-muted-foreground">
                    {format(new Date(selectedRfq.rfq.dueDate), 'PPP')}
                    {isOverdue(selectedRfq.rfq.dueDate) && (
                      <Badge variant="destructive" className="ml-2">Overdue</Badge>
                    )}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">Trade Category</h4>
                  <Badge variant="outline" className={getTradeColor(selectedRfq.rfq.tradeCategory)}>
                    {selectedRfq.rfq.tradeCategory}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">Status</h4>
                  <Badge className={getStatusColor(selectedRfq.rfq.status)}>
                    {selectedRfq.rfq.status}
                  </Badge>
                </div>
              </div>

              {/* Project Description */}
              {selectedRfq.rfq.description && (
                <div className="space-y-2">
                  <h4 className="font-medium">Project Description</h4>
                  <div className="bg-muted p-4 rounded-lg">
                    <p className="text-sm whitespace-pre-wrap">{selectedRfq.rfq.description}</p>
                  </div>
                </div>
              )}

              {/* AI Extracted Data */}
              {selectedRfq.rfq.extractedData && (
                <div className="space-y-2">
                  <h4 className="font-medium">Key Project Requirements</h4>
                  <div className="bg-muted p-4 rounded-lg space-y-2">
                    {selectedRfq.rfq.extractedData.projectSummary && (
                      <div>
                        <p className="text-sm font-medium">Project Summary:</p>
                        <p className="text-sm text-muted-foreground">{selectedRfq.rfq.extractedData.projectSummary}</p>
                      </div>
                    )}
                    {selectedRfq.rfq.extractedData.requirements && Array.isArray(selectedRfq.rfq.extractedData.requirements) && (
                      <div>
                        <p className="text-sm font-medium">Requirements:</p>
                        <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
                          {selectedRfq.rfq.extractedData.requirements.map((req: string, idx: number) => (
                            <li key={idx}>{req}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {selectedRfq.rfq.extractedData.contactName && (
                      <div>
                        <p className="text-sm font-medium">Contact:</p>
                        <p className="text-sm text-muted-foreground">
                          {selectedRfq.rfq.extractedData.contactName}
                          {selectedRfq.rfq.extractedData.contactEmail && (
                            <> ({selectedRfq.rfq.extractedData.contactEmail})</>
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Distribution Info */}
              <div className="space-y-2">
                <h4 className="font-medium">RFQ Status</h4>
                <div className="bg-muted p-4 rounded-lg space-y-2 text-sm">
                  <p>Received: {formatDistanceToNow(new Date(selectedRfq.sentAt), { addSuffix: true })}</p>
                  {selectedRfq.viewedAt && (
                    <p>Viewed: {formatDistanceToNow(new Date(selectedRfq.viewedAt), { addSuffix: true })}</p>
                  )}
                  {selectedRfq.declinedAt && (
                    <div>
                      <p>Declined: {formatDistanceToNow(new Date(selectedRfq.declinedAt), { addSuffix: true })}</p>
                      {selectedRfq.declineReason && (
                        <p className="text-muted-foreground">Reason: {selectedRfq.declineReason}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              {!selectedRfq.declinedAt && !isOverdue(selectedRfq.rfq.dueDate) && selectedRfq.rfq.status === 'Active' && (
                <div className="flex gap-2 pt-4 border-t">
                  <Button 
                    onClick={() => {
                      setShowBidForm(true);
                    }}
                    className="flex-1"
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Submit Bid
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => {
                      setShowDeclineDialog(true);
                    }}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Decline
                  </Button>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Bid Submission Form */}
      {showBidForm && selectedRfq && (
        <Dialog open={showBidForm} onOpenChange={setShowBidForm}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Submit Bid - {selectedRfq.rfq.projectName}</DialogTitle>
              <DialogDescription>
                Upload your bid package and review the extracted information before submission.
              </DialogDescription>
            </DialogHeader>
            <BidSubmissionForm 
              rfqId={selectedRfq.rfqId}
              onSuccess={() => {
                setShowBidForm(false);
                setSelectedRfq(null);
                queryClient.invalidateQueries({ queryKey: ['/api/contractors/rfqs'] });
                toast({ title: "Bid submitted successfully!" });
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

neonConfig.webSocketConstructor = ws;

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

// Parse DATABASE_URL to extract components safely
function parseDatabaseUrl(url: string) {
  try {
    const parsed = new URL(url);
    return {
      user: parsed.username,
      password: parsed.password,
      host: parsed.hostname,
      port: parseInt(parsed.port) || 5432,
      database: parsed.pathname.slice(1), // Remove leading slash
      ssl: parsed.searchParams.get('sslmode') === 'require'
    };
  } catch (error) {
    throw new Error("Invalid DATABASE_URL format");
  }
}

// Create connection configuration without exposing credentials in logs
const dbConfig = parseDatabaseUrl(process.env.DATABASE_URL);
const poolConfig = {
  user: dbConfig.user,
  password: dbConfig.password,
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  ssl: dbConfig.ssl,
  // Override toString to prevent credential exposure in logs
  toString: () => `[Pool Config for ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}]`
};

export const pool = new Pool(poolConfig);
export const db = drizzle({ client: pool, schema });
# Bidaible - AI-Powered Construction Bidding Platform

A cutting-edge construction RFQ (Request for Quote) and bidding platform that transforms procurement through sophisticated AI-driven bid analysis and competitive intelligence. Built with modern web technologies and featuring enterprise-level analytical capabilities that deliver "wow factor" insights for construction professionals.

## 🚀 Core Platform Features

### AI-Powered Bid Analysis
- **Complete AI Analysis Dashboard**: Three-tab system (Overview, Bid Analysis, AI Analysis) with comprehensive bid intelligence
- **Executive Summary Generation**: AI-powered summaries with key insights, recommendations, and risk factors
- **Competitive Bid Ranking**: Automated scoring and ranking with detailed reasoning and competitive positioning analysis
- **Market Intelligence**: Real-time price spread analysis, competitive positioning, and risk assessment with sub-3-second generation
- **Comprehensive Fallback System**: Robust AI pipeline (Groq → Gemini → OpenAI → Manual) ensuring 100% system reliability
- **Strategic Recommendations**: Actionable insights for bid evaluation and contractor selection with detailed risk profiling

### Document Processing & Management
- **Enhanced Multi-File Processing**: Support for 5-8 files per RFQ with intelligent file type classification (main, drawings, specifications, addendum, supporting)
- **Main File Priority Processing**: AI system focuses on main RFQ files for comprehensive extraction while supporting files contribute supplemental data
- **Construction-Specific AI Analysis**: Professional construction project analyst with industry-specific prompts and terminology
- **Comprehensive AI Summary Generation**: Rich markdown-formatted summaries with project overview, scope analysis, timeline insights, and professional recommendations
- **Large File Support**: Process files up to 250MB with smart storage strategy and extended AI processing timeouts (90 seconds)
- **Enhanced PDF Viewing**: Direct new-tab document viewing with optimized Object Storage integration
- **Smart Data Extraction**: Automated extraction of project details, contact information, requirements, deadlines, and professional insights
- **Document Intelligence**: AI-powered analysis with specialized construction RFQ processing and project complexity assessment

### Business Management & Security
- **Multi-Tenant Organization Support**: Complete organizational structure with isolated data access and management
- **Advanced Role-Based Access Control**: Comprehensive 4-tier permission system (SuperUser > Admin > Editor > Viewer) with organization-scoped access
- **User Management Interface**: Full user administration with role assignment, audit trails, and access monitoring
- **Security & Compliance**: Comprehensive audit logging for role changes and access attempts with real-time monitoring
- **Advanced Contractor Profiles**: Comprehensive database with 29 trade specialties and verification status
- **Real-Time Bid Tracking**: Complete bid lifecycle management with deadline monitoring and status updates
- **Dashboard Analytics**: Performance metrics, project statistics, and competitive intelligence with embedded Bid Management Dashboard featuring full-width AI-powered bid analysis prominently displayed on main dashboard

### Enterprise-Grade Security & API Management
- **Comprehensive API Key Management**: Full CRUD operations for API keys through intuitive Settings interface
- **JWT-Based Authentication**: Secure programmatic access with scoped permissions (read-only, upload-only, full-access)
- **Advanced Security Features**: API key masking, visibility controls, copy-to-clipboard functionality, and confirmation dialogs
- **Usage Analytics & Monitoring**: Real-time rate limiting, usage tracking, and comprehensive audit trails
- **🔐 Enhanced Security Hardening**: Multi-phase enterprise security implementation with CSP, HSTS, session management, and API key protection
- **🛡️ Automatic Threat Protection**: Failed attempt tracking, IP restrictions, key lockout mechanisms, and suspicious activity detection
- **🔄 Key Rotation System**: Secure API key rotation with dual-token validation and administrative controls
- **📊 Security Monitoring**: Real-time statistics, locked key detection, and comprehensive audit logging

### Comprehensive Bid Comparison & Export System
- **✅ SIDE-BY-SIDE BID ANALYSIS**: Complete comparative analysis with detailed cost code breakdowns using CSI MasterFormat standards
- **✅ DETAILED CSV EXPORT**: Individual cost code line items showing Contractor → Major Cost Code → Cost Code Detail → Description → Price format
- **✅ SMART EXPORT FUNCTIONALITY**: "Export Accepted Bids CSV" button in ComprehensiveBidComparison component for detailed budget planning
- **Enhanced QuickBooks Integration**: Complete cost code breakdown export with 760+ consolidated cost codes from bid line items
- **Advanced Sage ERP Sync**: Bidirectional project synchronization with comprehensive cost code data exchange
- **Multi-System Project Budget Sync**: Dedicated endpoint supporting QuickBooks, Sage, Salesforce, and HubSpot with system-specific formatting
- **Comprehensive Cost Code Data**: Detailed line items including cost codes, descriptions, quantities, unit prices, totals, and categories
- **Category-Based Analysis**: Automatic grouping and budget summaries by cost code categories for enhanced ERP workflow integration
- **System-Specific Formatting**: Custom data transformation for each ERP/CRM system (QuickBooks items, Sage job cost codes, Salesforce products, HubSpot line items)
- **Production-Ready Endpoints**: Enterprise-grade authentication, ownership verification, and comprehensive error handling
- **Data Export**: CSV and JSON export formats with date filtering for RFQ data

### Complete Real-Time Notification System
- **Multi-Channel Notifications**: Email, in-app, and SMS delivery options with user preference management
- **Business Workflow Integration**: Automatic notifications for RFQ uploads, bid submissions, and bid status changes
- **Resend Email Service**: Professional email delivery with bidaible.com domain verification and message ID tracking
- **Notification Preferences UI**: Comprehensive user controls for notification types, delivery methods, and quiet hours
- **Real-Time Email Notifications**: Instant delivery for RFQ processing completion, new bid submissions, and bid acceptances/rejections
- **Email Audit Logging**: Complete tracking of all email events (attempted, sent, failed) integrated with business audit system
- **Template System**: Professional email templates with personalized content and proper business data formatting
- **Production Email System**: Verified domain authentication with enterprise-grade delivery tracking and error handling

### User Experience & Compliance
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Theme System**: Custom light/dark themes with Plus Jakarta Sans typography
- **Real-Time Updates**: Optimistic UI updates with TanStack Query
- **File Upload**: Drag-and-drop interface with validation and progress indicators
- **Settings Management**: Comprehensive settings interface with tabbed navigation for API keys, preferences, and account management
- **Terms & Conditions System**: Professional terms modal with markdown formatting, user acceptance tracking, database audit trails, and automatic grandfathering for existing users
- **Legal Compliance**: Complete terms and conditions management with proper markdown rendering, acceptance validation, and comprehensive audit logging

## 🛠 Technology Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side navigation
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query for server state
- **Build Tool**: Vite with hot module replacement
- **Forms**: React Hook Form with Zod validation
- **Markdown Rendering**: React Markdown with custom component styling for terms and documentation

### Backend
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Authentication**: Dual authentication system (Replit Auth + JWT API keys)
- **Session Management**: PostgreSQL-backed sessions with connect-pg-simple
- **File Processing**: Multer middleware with Object Storage integration
- **API Security**: Rate limiting, audit logging, and comprehensive middleware

### Database & AI
- **Database**: PostgreSQL (Neon serverless) with 30+ strategic indexes and multi-file batch processing support
- **ORM**: Drizzle ORM with type-safe queries and enhanced schema for multi-file RFQ processing
- **User Management**: Enhanced user schema with terms acceptance tracking (terms_accepted, terms_accepted_at fields)
- **Audit Compliance**: Complete audit trails for terms acceptance with timestamps and user tracking
- **Enhanced AI Processing**: Construction-specific AI models with comprehensive summary generation (3000 token limit)
- **AI Models**: OpenAI OSS-120b, Google Gemini 2.5 Pro/Flash with OpenAI GPT-4.1-mini fallback and robust retry logic
- **Multi-File Intelligence**: Priority-based processing with main RFQ file focus and batch metadata tracking
- **Professional Analysis Framework**: Construction industry terminology, project complexity assessment, and contractor insights
- **Caching**: In-memory caching with TTL and LRU eviction policies
- **Performance**: Connection pooling, optimized query patterns, and intelligent file processing

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL database (Neon recommended)
- Environment variables (see Configuration section)

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone <repository-url>
cd bidaible
npm install
```

### 2. Environment Setup
Create a `.env` file with the required variables:

```env
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# AI Services
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
GROQ_API_KEY=your_groq_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key

# Authentication
SESSION_SECRET=your_session_secret

# AI Model Configuration (optional)
PRIMARY_MODEL=openai  # 'openai', 'gemini', or 'groq'
```

### 3. Database Setup
```bash
# Push schema to database
npm run db:push

# Generate database client (if needed)
npm run db:generate
```

### 4. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:5000`

## 🤖 AI-Powered Bid Analysis System

### Comprehensive Bid Intelligence
- **Deep Analytical Processing**: Multi-factor evaluation with competitive scoring, risk assessment, and strategic recommendations
- **Market Intelligence**: Real-time competitive analysis with pricing trends and positioning insights
- **Risk Assessment**: Advanced timeline evaluation, contractor profiling, and project risk identification
- **Predictive Analytics**: Success modeling preventing poor decisions and identifying optimal value propositions

### Document Processing Pipeline
- **Supported File Types**: PDF, TXT, CSV (250MB maximum per upload)
- **Text Extraction**: PDF.js extracts clean text from PDFs (24,000+ characters supported)
- **AI Processing**: Google Gemini 2.5 Pro with OpenAI GPT-4.1-mini fallback for reliability
- **Smart Data Mapping**: Extracted fields automatically mapped to database schema with validation
- **Auto-Save**: Complete RFQ/bid records created without manual input

### AI Analysis Features
- **Competitive Scoring**: Multi-dimensional bid evaluation considering price, timeline, contractor qualifications, and completeness
- **Risk Detection**: Automated identification of potential project risks and timeline concerns
- **Market Positioning**: Comparative analysis showing how bids rank against competition
- **Strategic Recommendations**: Actionable insights for bid acceptance, negotiation, or rejection decisions

### Extracted Data Fields
- **Project Information**: Description, summary, scope, location, and technical specifications
- **Financial Data**: Bid amounts, timeline estimates, and cost breakdowns
- **Contact Details**: Contractor information, primary contacts, and communication preferences
- **Requirements Analysis**: Comprehensive evaluation of project requirements and contractor capabilities
- **Risk Factors**: Timeline feasibility, contractor reliability, and project complexity assessment

## 📁 Project Structure

```
bidaible/
├── client/                 # Frontend React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Application pages
│   │   ├── hooks/         # Custom React hooks
│   │   └── lib/           # Utility functions
├── server/                # Backend Express application
│   ├── services/          # Business logic services
│   │   ├── aiService.ts   # AI document processing
│   │   ├── bidAnalysisService.ts # Bid intelligence & competitive analysis
│   │   └── cacheService.ts # In-memory caching with TTL
│   ├── middleware/        # Security and authentication middleware
│   ├── routes.ts          # API route definitions with dual auth
│   ├── storage.ts         # Database operations with Drizzle ORM
│   └── replitAuth.ts      # Replit + JWT authentication setup
├── shared/                # Shared code between client/server
│   └── schema.ts          # Database schema and types
└── uploads/               # File upload directory
```

## 🔧 Configuration

### AI Model Configuration
Set the `PRIMARY_MODEL` environment variable to choose the primary AI service:
- `openai`: Use OpenAI GPT-4o-mini as primary
- `gemini`: Use Google Gemini 2.0 Flash as primary
- `groq`: Use Groq Llama as primary

All models have intelligent fallback to other providers for reliability.

### Database Schema
The application uses a comprehensive relational schema with 30+ strategic indexes:
- `users`: User accounts and profiles with role-based permissions
- `contractors`: Contractor information, verification status, and 29 trade categories
- `contractor_favorites`: GC-managed favorite contractor relationships
- `rfqs`: Request for Quote records with AI-extracted data and analysis
- `rfq_documents`: Uploaded document references with Object Storage links
- `rfq_distribution`: Smart distribution tracking with favorites/broadcast modes
- `bids`: Bid submissions with AI analysis, competitive scoring, and risk assessment
- `api_keys`: JWT-based API authentication with scoped permissions and usage tracking
- `api_key_usage`: Comprehensive usage analytics and rate limiting data
- `notifications`: Real-time notification system with multi-channel delivery tracking
- `notification_preferences`: User-specific notification settings and delivery preferences
- `notification_delivery_log`: Complete audit trail of all notification attempts and deliveries

### File Upload Limits
- Maximum file size: 50MB
- Supported formats: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, CSV, TXT
- Files stored in `/uploads` directory

## 🔒 Authentication & Security

### Enterprise-Grade Security Architecture
- **Multi-Layered Security**: Three-phase security hardening with comprehensive threat protection
- **Content Security Policy**: Advanced CSP with AI service integration and XSS prevention
- **Session Security**: 72-hour TTL with sliding expiration and role-based invalidation
- **Advanced API Key Security**: Enhanced generation, verification, rotation, and lockout mechanisms

### Dual Authentication System
- **Session-Based Authentication**: Standard web interface using Replit Auth with PostgreSQL-backed sessions
- **API Key Authentication**: Programmatic access using JWT tokens with comprehensive management interface
- **IP Restrictions**: API key-level IP whitelisting for enhanced security
- **Failed Attempt Tracking**: Automatic lockout after 5 failed attempts with 15-minute cooldown

### User Roles
- **SuperUser**: Full system access and management
- **Admin**: User management and system configuration
- **Editor**: Create and edit RFQs and bids
- **Viewer**: Read-only access to data

### Enhanced API Key Management Features
- **Create API Keys**: Generate new keys with custom names, scoped permissions, and security tokens
- **Advanced Security Controls**: Masked key display, visibility toggle, copy functionality, and IP restrictions
- **Permission Scoping**: read-only, upload-only, or full-access permissions with environment targeting
- **Usage Monitoring**: Real-time rate limiting, usage analytics, and comprehensive audit trails
- **Key Lifecycle**: Edit metadata, deactivate keys, secure deletion, and automatic rotation warnings
- **Security Features**: Failed attempt tracking, automatic lockout, key rotation system, and compromise detection
- **Administrative Controls**: Security statistics, rotation monitoring, and session invalidation capabilities
- **Audit Trail**: Complete tracking of key creation, usage, modifications, security events, and rotation history

### Advanced Session & Security Management
- **PostgreSQL-backed sessions** for scalability with 72-hour TTL and sliding expiration
- **Automatic session refresh** with token management and role-based invalidation
- **Secure cookie configuration** for production with sameSite: 'strict' and security flags
- **Comprehensive security headers** including CSP, HSTS, and CORS with AI service integration
- **Rate limiting protection** with suspicious activity detection and audit logging
- **Administrative security controls** for session management and threat response

## 🔒 Enterprise Security Implementation (August 2025)

### 🛡️ Comprehensive Security Hardening - All Phases Complete
**✅ PHASE 1: Security Headers & CORS**
- **Content Security Policy**: Comprehensive directives preventing XSS attacks with AI service integration
- **HTTP Strict Transport Security**: 1-year max-age with subdomain inclusion and preloading
- **CORS Configuration**: Environment-based origin validation with credentials support
- **Input Sanitization**: Automatic removal of control characters and null byte injection
- **Rate Limiting**: Security endpoint protection (100 requests/15 minutes) with suspicious activity logging

**✅ PHASE 2: Session Security Enhancement**
- **Session TTL Optimization**: Reduced from 7 days to 72 hours with sliding expiration (rolling: true)
- **Enhanced Cookie Security**: sameSite: 'strict' for CSRF protection with secure flags
- **Role Change Security**: Automatic session invalidation when user roles change
- **PostgreSQL Session Store**: Advanced session management with cleanup and error handling
- **Administrative Controls**: Session invalidation endpoints for security management

**✅ PHASE 3: API Key Security System**
- **Enhanced Key Generation**: Security tokens, IP restrictions, environment targeting, configurable expiration
- **Advanced Verification**: Multi-layer validation with JWT issuer checking and salted hashing
- **Automatic Lockout**: 5 failed attempts trigger 15-minute key lockout with compromise detection
- **Key Rotation**: Secure rotation with dual-token validation and automatic old key deactivation
- **Security Statistics**: Advanced analytics showing locked keys, rotation needs, and usage patterns
- **Administrative Controls**: New endpoints for key rotation, security stats, and monitoring

### 🚀 Recent Platform Updates (August 2025)

### "Wow Factor" Achievements ✨
- **✅ COMPREHENSIVE BID COMPARISON SYSTEM COMPLETED**: Side-by-side bid analysis with complete CSI MasterFormat cost code breakdown
- **✅ DETAILED CSV EXPORT FUNCTIONALITY**: Individual cost code line items (16.010, 16.015, etc.) with contractor, description, and pricing details  
- **✅ SMART EXPORT LOGIC**: Automatically exports detailed cost breakdowns for accepted bids, summary data for all others
- **AI Bid Analysis System**: Deep analytical consideration with competitive scoring, risk assessment, and strategic recommendations
- **Market Intelligence Dashboard**: Real-time competitive analysis delivering negotiation power and decision support
- **Predictive Success Modeling**: Multi-factor evaluation preventing poor decisions and identifying optimal value propositions
- **Comprehensive Risk Detection**: Advanced timeline evaluation and contractor profiling catching risks humans miss

### Enterprise Features
- **Complete API Key Management Interface**: Full CRUD operations with intuitive Settings page including key masking, visibility controls, and secure deletion
- **JWT API Authentication**: Complete programmatic access with scoped permissions (read-only, upload-only, full-access)
- **Advanced Security Controls**: API key masking, copy-to-clipboard functionality, and confirmation dialogs for destructive actions
- **Rate Limiting & Analytics**: Configurable per-key limits with comprehensive usage tracking and audit trails
- **ERP Integration**: QuickBooks export and Sage ERP synchronization capabilities
- **Advanced Caching**: In-memory caching with TTL, LRU eviction, and pattern invalidation

### Performance & Security
- **Database Optimization**: 30+ strategic indexes for optimal query performance with large datasets
- **Security Middleware**: Audit logging, rate limiting, security headers, and input sanitization
- **Backup Systems**: Automated database and file backup with retention policies
- **Monitoring**: Admin-only performance and security monitoring endpoints

## 📊 Dashboard Features

### AI-Powered Bid Management Dashboard
- **Three-Tab Interface**: Overview, Bid Analysis, and AI Analysis for comprehensive bid evaluation
- **Executive Summary Panel**: AI-generated insights, recommendations, and risk factors
- **Competitive Bid Ranking**: Automated scoring with detailed reasoning and market positioning
- **Market Analysis**: Price spreads, competitive positioning, and risk assessment
- **Real-time Generation**: Sub-3-second AI analysis powered by Groq Kimi K2 model

### Statistics Overview
- Active RFQs count
- Total bids submitted
- Average response time
- Success rate metricstrics

### MatIQ (Material Intelligence)
- Real-time construction material pricing
- 30-day trend indicators with directional arrows
- CSI standard cost codes integration
- Market trend predictions

## 🎨 UI/UX Design

### Design System
- **Light Theme**: Warm beige background (#e7e5e4) with orange primary (#f75100)
- **Dark Theme**: Dark brown background (#1e1b18) with orange accent (#f38620)
- **Typography**: Plus Jakarta Sans font family
- **Components**: shadcn/ui component library

### Responsive Design
- Mobile-first approach
- Adaptive layouts for all screen sizes
- Touch-friendly interface elements
- Optimized for construction industry workflows

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

### Environment Variables for Production
Ensure all required environment variables are set:
- Database connection string
- AI API keys
- Session secrets
- Authentication configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Code Style
- TypeScript for all new code
- Follow existing naming conventions
- Use proper type definitions
- Add JSDoc comments for functions

## 📝 API Documentation

### Core RFQ & Bid Endpoints
- `POST /api/rfqs` - Create RFQ with AI document processing
- `GET /api/rfqs` - List all RFQs with filtering and pagination
- `GET /api/rfqs/:id` - Get specific RFQ with AI summary and analysis
- `GET /api/rfqs/:rfqId/bids` - List all bids for an RFQ
- `POST /api/rfqs/:rfqId/bids` - Submit bid with AI analysis
- `POST /api/bids/:bidId/action` - Accept/reject/request info for bids

### AI Analysis & Intelligence
- `GET /api/analytics/bid-analysis/:rfqId` - Comprehensive bid analysis with competitive insights
- `POST /api/bids/:bidId/analysis` - Generate deep AI analysis for specific bid
- `GET /api/bids/:bidId/summary` - Get bid summary with risk assessment

### API Key Management (JWT Authentication)
- `POST /api/auth/api-keys` - Generate new API key with scoped permissions
- `GET /api/auth/api-keys` - List user's API keys (metadata only)
- `GET /api/auth/api-keys/:id/stats` - Get API key usage statistics
- `PATCH /api/auth/api-keys/:id` - Update API key settings
- `DELETE /api/auth/api-keys/:id` - Revoke API key

### User Management & Security
- `GET /api/admin/users` - List users (Admin/SuperUser only)
- `PATCH /api/admin/users/:userId/role` - Update user roles with audit logging
- `GET /api/admin/audit/roles` - Role change audit logs
- `GET /api/admin/audit/access` - Access attempt audit logs

### ERP & Integration Endpoints with Cost Code Synchronization
- `GET /api/integrations/quickbooks` - Export financial data with detailed cost code breakdown for QuickBooks integration
- `POST /api/integrations/sage` - Bidirectional sync with Sage ERP including comprehensive cost code data export
- `GET /api/integrations/project-budget/:rfqId/:bidId?system=quickbooks` - **NEW**: Comprehensive project budget sync with cost codes for multiple ERP/CRM systems
  - **Query Parameters**: `system` (quickbooks, sage, salesforce, hubspot) - formats data for specific target system
  - **Returns**: Complete cost code breakdown with system-specific field mapping from bid_line_items table (760+ consolidated cost codes)
  - **Data Includes**: Cost codes, descriptions, quantities, unit prices, totals, categories, and system-specific formatting
- `GET /api/integrations/export/rfqs` - Export RFQ data (CSV/JSON)

#### Cost Code Integration Features
- **Comprehensive Line Item Export**: All bid line items with cost codes, descriptions, quantities, unit prices, and totals
- **System-Specific Formatting**: Automatic data transformation for target ERP/CRM systems (QuickBooks items, Sage job cost codes, Salesforce products, HubSpot line items)
- **Category Analysis**: Budget summaries grouped by cost code categories for enhanced ERP workflow integration
- **Production Ready**: Enterprise authentication, ownership verification, and comprehensive error handling

### Notification System Endpoints
- `GET /api/notifications/preferences` - Get user notification preferences and delivery settings
- `PATCH /api/notifications/preferences` - Update notification preferences with granular controls
- `POST /api/notifications/send-custom-email` - Send custom notification emails for testing and communication
- `GET /api/notifications/history` - Retrieve notification delivery history and status tracking
- `POST /api/notifications/mark-read` - Mark notifications as read with batch operations support

### Contractor Management
- `GET /api/contractors` - List contractors with trade filtering
- `GET /api/contractors/rfqs/all` - Get all active RFQs for contractors
- `POST /api/contractors/favorites` - Manage favorite contractor relationships

### Authentication & Permissions
The platform supports comprehensive multi-level authentication and authorization:
- **Session-based Authentication**: Standard web authentication via Replit Auth with role-based access control
- **API Key Authentication**: JWT tokens for programmatic access with scoped permissions:
  - `read-only`: GET requests for data retrieval
  - `upload-only`: POST requests for data creation and file uploads  
  - `full-access`: All operations (GET, POST, PUT, PATCH, DELETE)
- **Role-Based Access Control**: 4-tier permission system with organization-scoped data access:
  - `SuperUser`: Full system access across all organizations
  - `Admin`: Complete organization management and user administration
  - `Editor`: Content creation and modification within organization
  - `Viewer`: Read-only access to organization data

### AI Processing Features
All document uploads and bid submissions automatically trigger comprehensive AI processing:
- **Document Analysis**: Project details, specifications, contact information, timelines
- **Bid Analysis Dashboard**: Complete AI-powered analysis with Executive Summary, Bid Ranking, and Market Analysis panels
- **Rapid AI Generation**: Sub-3-second analysis powered by Groq Kimi K2 model with comprehensive fallback system
- **Competitive Intelligence**: Market positioning, pricing analysis, risk assessment, and strategic recommendations
- **Real-time Insights**: Instant bid evaluation with competitive scoring, reasoning, and actionable recommendations
- **Risk Assessment**: Timeline feasibility, contractor reliability, project complexity
- **Strategic Recommendations**: Actionable insights for bid evaluation and decision making

## 🎯 Core Features

#### 🚀 **Pre-Launch Waitlist System**
Beautiful landing page with waitlist functionality to capture early user interest. Includes real-time count display, email validation, and company information collection for targeted launch communications.

#### 🤖 **Multi-Provider AI Document Processing**
Upload RFQ documents (PDF, Word, Excel, PowerPoint) and watch as AI automatically extracts project details, requirements, timelines, and contact information. The system uses OpenAI GPT-4o-mini, Google Gemini 2.0 Flash, or Groq Llama with intelligent fallback for maximum reliability.

#### 📊 **Advanced Bid Analytics & AI Analysis**
Get comprehensive AI-powered analysis of all bids with executive summaries, competitive ranking, market analysis, and risk assessment. The system provides sub-3-second generation of detailed insights using multiple AI providers.

#### 👥 **Smart Contractor Management**
- **29 Trade Categories**: From general contractors to specialized trades
- **Verification System**: License, insurance, and capability verification
- **Favorites Management**: Build and manage preferred contractor lists
- **Intelligent Matching**: AI-powered contractor-to-project matching

## 🐛 Troubleshooting

### Common Issues

**File Upload Errors**
- Check file size limits (50MB max)
- Verify supported file formats
- Ensure upload directory permissions

**AI Processing Failures**
- Verify API keys are set correctly
- Check network connectivity
- Review file content quality

**Database Connection Issues**
- Confirm DATABASE_URL format
- Check database server status
- Verify connection pool settings

### Debug Mode
Enable debug logging by setting:
```env
NODE_ENV=development
```

## 📞 Support

For technical support or questions:
- Review the troubleshooting section
- Check the project issues on GitHub
- Refer to the code documentation in `/docs`

## 📄 License

This project is proprietary software. All rights reserved.

---

Built with ❤️ for the construction industry

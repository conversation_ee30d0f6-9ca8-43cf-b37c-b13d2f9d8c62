import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { insertContractorSchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Upload, ChevronRight, ChevronLeft } from "lucide-react";
import { z } from "zod";

const contractorFormSchema = insertContractorSchema.extend({
  tradeTypes: z.array(z.string()).min(1, "Please select at least one trade type"),
  companyWebsite: z.string().optional(),
});

type ContractorFormData = z.infer<typeof contractorFormSchema>;

const tradeOptions = [
  { value: "general_contractor", label: "General Contractor" },
  { value: "electrician", label: "Electrical" },
  { value: "plumber", label: "Plumbing" },
  { value: "hvac", label: "HVAC" },
  { value: "sitework", label: "Site Work/Excavation" },
  { value: "concrete", label: "Concrete" },
  { value: "masonry", label: "Masonry" },
  { value: "structural_steel", label: "Structural Steel" },
  { value: "carpentry", label: "Carpentry" },
  { value: "roofing", label: "Roofing" },
  { value: "waterproofing", label: "Waterproofing" },
  { value: "insulation", label: "Insulation" },
  { value: "drywall", label: "Drywall" },
  { value: "flooring", label: "Flooring" },
  { value: "painting", label: "Painting" },
  { value: "fire_protection", label: "Fire Protection" },
  { value: "security_systems", label: "Security Systems" },
  { value: "landscaping", label: "Landscaping" },
  { value: "asphalt_paving", label: "Asphalt/Paving" },
  { value: "surveying", label: "Surveying" },
  { value: "environmental", label: "Environmental Services" },
  { value: "demolition", label: "Demolition" },
  { value: "utilities", label: "Utilities" },
  { value: "telecommunications", label: "Telecommunications" },
  { value: "glazing", label: "Glazing/Windows" },
  { value: "metal_fabrication", label: "Metal Fabrication" },
  { value: "elevator", label: "Elevator/Escalator" },
  { value: "architectural_millwork", label: "Architectural Millwork" },
  { value: "specialty_other", label: "Other Specialty" },
];

const steps = [
  { title: "Business Identity", fields: 8 },
  { title: "Classification", fields: 5 },
  { title: "Credentials", fields: 6 }
];

export function ContractorProfileForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedTrades, setSelectedTrades] = useState<string[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<ContractorFormData>({
    resolver: zodResolver(contractorFormSchema),
    mode: "onSubmit",
    defaultValues: {
      companyName: "",
      companyWebsite: "",
      legalStructure: "",
      taxId: "",
      businessAddress: "",
      contactEmail: "",
      contactPhone: "",
      tradeTypes: [],
      unionStatus: "",
      yearsInBusiness: undefined,
      licenseNumber: "",
      licenseExpiration: "",
    },
  });

  const mutation = useMutation({
    mutationFn: async (data: ContractorFormData) => {
      const response = await apiRequest("POST", "/api/contractors", {
        ...data,
        tradeTypes: selectedTrades,
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Contractor profile created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/contractors"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ContractorFormData) => {
    mutation.mutate(data);
  };

  const handleTradeToggle = (tradeValue: string) => {
    const newTrades = selectedTrades.includes(tradeValue)
      ? selectedTrades.filter(t => t !== tradeValue)
      : [...selectedTrades, tradeValue];
    
    setSelectedTrades(newTrades);
    form.setValue("tradeTypes", newTrades);
  };

  const handleNextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = ((currentStep + 1) / 3) * 100;

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-xl">Contractor Profile Setup</CardTitle>
        <p className="text-sm text-muted-foreground">
          Complete your contractor profile to participate in bidding opportunities
        </p>
        <div className="space-y-2">
          <Progress value={progress} className="w-full" />
          <p className="text-sm text-muted-foreground">{Math.round(progress)}% Complete</p>
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Step Navigation */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              {steps.map((step, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setCurrentStep(index)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    currentStep === index
                      ? "bg-primary text-primary-foreground"
                      : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                  }`}
                >
                  {index + 1}. {step.title}
                </button>
              ))}
            </div>
          </div>

          {/* Step Content */}
          {currentStep === 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Business Identity & Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Company Name *</Label>
                    <Input
                      id="companyName"
                      {...form.register("companyName")}
                      placeholder="Enter company name"
                    />
                    {form.formState.errors.companyName && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.companyName.message}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="companyWebsite">Company Website</Label>
                    <Input
                      id="companyWebsite"
                      type="text"
                      {...form.register("companyWebsite")}
                      placeholder="https://www.example.com"
                    />
                    {form.formState.errors.companyWebsite && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.companyWebsite.message}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="legalStructure">Legal Structure</Label>
                    <Select onValueChange={(value) => form.setValue("legalStructure", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select structure" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="corporation">Corporation</SelectItem>
                        <SelectItem value="llc">LLC</SelectItem>
                        <SelectItem value="partnership">Partnership</SelectItem>
                        <SelectItem value="sole_proprietorship">Sole Proprietorship</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="taxId">Tax ID/EIN *</Label>
                    <Input
                      id="taxId"
                      {...form.register("taxId")}
                      placeholder="Enter tax ID or EIN"
                    />
                    {form.formState.errors.taxId && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.taxId.message}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Primary Contact Email *</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      {...form.register("contactEmail")}
                      placeholder="Enter contact email"
                    />
                    {form.formState.errors.contactEmail && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.contactEmail.message}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Contact Phone</Label>
                    <Input
                      id="contactPhone"
                      {...form.register("contactPhone")}
                      placeholder="Enter contact phone"
                    />
                  </div>
                  
                  <div className="md:col-span-2 space-y-2">
                    <Label htmlFor="businessAddress">Business Address *</Label>
                    <Textarea
                      id="businessAddress"
                      {...form.register("businessAddress")}
                      placeholder="No P.O. boxes allowed"
                      rows={3}
                    />
                    {form.formState.errors.businessAddress && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.businessAddress.message}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Classification & Capability */}
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle>Classification & Capability</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-3">
                    <Label>Trade/Specialty Types *</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {tradeOptions.map((trade) => (
                        <div key={trade.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={trade.value}
                            checked={selectedTrades.includes(trade.value)}
                            onCheckedChange={() => handleTradeToggle(trade.value)}
                          />
                          <Label 
                            htmlFor={trade.value}
                            className="text-sm font-normal cursor-pointer"
                          >
                            {trade.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                    {form.formState.errors.tradeTypes && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.tradeTypes.message}
                      </p>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="unionStatus">Union Status</Label>
                      <Select onValueChange={(value) => form.setValue("unionStatus", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="union">Union</SelectItem>
                          <SelectItem value="non-union">Non-Union</SelectItem>
                          <SelectItem value="open-shop">Open Shop</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="yearsInBusiness">Years in Business</Label>
                      <Input
                        id="yearsInBusiness"
                        type="number"
                        {...form.register("yearsInBusiness", { 
                          valueAsNumber: true
                        })}
                        placeholder="Enter years"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Credentials & Compliance */}
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>Credentials & Compliance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="licenseNumber">Contractor License Number</Label>
                      <Input
                        id="licenseNumber"
                        {...form.register("licenseNumber")}
                        placeholder="Enter license number"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="licenseExpiration">License Expiration Date</Label>
                      <Input
                        id="licenseExpiration"
                        type="date"
                        {...form.register("licenseExpiration")}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>License Documents</Label>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
                      <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground mb-2">Upload license documents</p>
                      <Button type="button" variant="outline" size="sm">
                        Select Files
                      </Button>
                      <p className="text-xs text-muted-foreground mt-2">
                        Supported formats: PDF, JPG, PNG (Max 10MB per file)
                      </p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Insurance Certificates</Label>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
                      <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground mb-2">Upload insurance certificates</p>
                      <Button type="button" variant="outline" size="sm">
                        Select Files
                      </Button>
                      <p className="text-xs text-muted-foreground mt-2">
                        Supported formats: PDF (Max 10MB per file)
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </form>
      </CardContent>
      
      <div className="p-6 border-t flex justify-between">
        <Button variant="ghost" type="button">
          Save & Continue Later
        </Button>
        <div className="flex space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevStep}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          {currentStep < 2 ? (
            <Button type="button" onClick={handleNextStep}>
              Next Section
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              type="submit"
              disabled={mutation.isPending}
              onClick={form.handleSubmit(onSubmit)}
            >
              {mutation.isPending ? "Creating..." : "Complete Profile"}
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
}

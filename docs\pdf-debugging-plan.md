# PDF Text Extraction Debugging Plan

## Problem Statement
PDF text extraction worked previously but now fails, extracting only 29 characters ("--- Page 1 --- --- Page 2 ---") instead of actual document content. Both pdf-parse and PDF.js libraries fail identically.

## Systematic Debugging Approach

### Phase 1: Environment & Library Verification (5 minutes)

#### Step 1.1: Check Current Library Versions
```bash
# Check package versions that may have auto-updated
npm ls pdf-parse
npm ls pdfjs-dist
node --version
```

#### Step 1.2: Verify PDF File Integrity
```bash
# Check if PDF is readable by system tools
file attached_assets/Painting\ -\ Kaser.pdf
head -c 100 attached_assets/Painting\ -\ Kaser.pdf
```

#### Step 1.3: Test with Simple PDF
Create a minimal test PDF to isolate if issue is file-specific or system-wide.

### Phase 2: Deep PDF Structure Analysis (10 minutes)

#### Step 2.1: Raw PDF Content Inspection
Log the first 1000 bytes of PDF buffer to verify:
- PDF header (%PDF-x.x)
- Basic structure integrity
- No truncation during file upload

#### Step 2.2: PDF.js Detailed Logging
Add comprehensive logging to see exactly what PDF.js returns:
```javascript
console.log('PDF metadata:', {
  numPages: pdf.numPages,
  info: await pdf.getMetadata(),
  fingerprint: pdf.fingerprint
});

for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
  const page = await pdf.getPage(pageNum);
  console.log(`Page ${pageNum} info:`, {
    pageIndex: page.pageIndex,
    view: page.view,
    rotate: page.rotate
  });
  
  const textContent = await page.getTextContent();
  console.log(`Page ${pageNum} textContent:`, {
    itemsLength: textContent.items?.length,
    stylesLength: textContent.styles?.length,
    rawItems: textContent.items,
    rawStyles: textContent.styles
  });
}
```

#### Step 2.3: Alternative PDF.js Extraction Methods
Test different extraction approaches:
1. `page.getOperatorList()` - Raw PDF operations
2. `page.getAnnotations()` - Form fields and annotations
3. Different PDF.js options (enableXfa, etc.)

### Phase 3: Library Comparison Testing (15 minutes)

#### Step 3.1: Test pdf-parse in Isolation
Create standalone test outside the main application:
```javascript
const fs = require('fs');
const pdfParse = require('pdf-parse');

async function testPdfParse() {
  const buffer = fs.readFileSync('path/to/pdf');
  try {
    const data = await pdfParse(buffer);
    console.log('pdf-parse success:', {
      textLength: data.text.length,
      numPages: data.numpages,
      textPreview: data.text.substring(0, 500)
    });
  } catch (error) {
    console.error('pdf-parse failed:', error);
  }
}
```

#### Step 3.2: Try Alternative Libraries
Test with different PDF extraction libraries:
1. `pdf2json` - Different parsing approach
2. `pdf-text-extract` - Node.js wrapper
3. `node-pdf-extractor` - Alternative implementation

### Phase 4: System Environment Investigation (10 minutes)

#### Step 4.1: Node.js and Dependencies
```bash
# Check if Node.js version changed
cat package-lock.json | grep -A5 -B5 "pdfjs-dist\|pdf-parse"
# Compare with git history if available
```

#### Step 4.2: Replit Environment Changes
Check for recent Replit platform updates that might affect:
- File system permissions
- Memory limits
- Native library availability

#### Step 4.3: Buffer Handling Verification
Log buffer at each processing stage:
```javascript
console.log('Upload buffer:', {
  length: buffer.length,
  first100: buffer.slice(0, 100),
  isPdfHeader: buffer.slice(0, 4).toString() === '%PDF'
});
```

### Phase 5: Alternative Extraction Strategies (20 minutes)

#### Step 5.1: Raw PDF Stream Parsing
Attempt manual PDF content stream extraction:
```javascript
// Look for stream objects containing text
const pdfString = buffer.toString('latin1');
const streamMatches = pdfString.match(/stream\s+(.*?)\s+endstream/gs);
console.log('Found streams:', streamMatches?.length);
```

#### Step 5.2: External Service Testing
If libraries fail, test external PDF processing:
1. Browser-based PDF.js (client-side processing)
2. External API service (Adobe PDF Services)
3. OCR fallback (Tesseract.js)

### Phase 6: Rollback Strategy (10 minutes)

#### Step 6.1: Version Rollback
If version mismatch found:
```bash
# Pin to specific working versions
npm install pdfjs-dist@3.11.174  # or last known working version
npm install pdf-parse@1.1.1
```

#### Step 6.2: Code Rollback
Revert to simplest known working implementation and build up incrementally.

## Expected Outcomes

### Success Indicators:
- Extract >1000 characters from test PDF
- Identify specific component causing failure
- AI summaries show actual document content

### Failure Points to Identify:
1. **Library Issue**: Version mismatch or breaking change
2. **Environment Issue**: Node.js/Replit platform change
3. **File Format Issue**: PDF encoding incompatibility
4. **Buffer Issue**: File corruption during upload/processing
5. **Configuration Issue**: Missing options or wrong settings

## Implementation Priority

### Immediate (Do First):
1. **Step 1.1** - Check library versions
2. **Step 2.1** - Verify PDF buffer integrity
3. **Step 2.2** - Detailed PDF.js logging

### Quick Wins (If Above Reveals Issue):
1. Version rollback if mismatch found
2. Buffer fix if corruption detected
3. Alternative library if PDF.js completely broken

### Deep Investigation (If Basic Steps Fail):
1. Raw PDF parsing
2. External service integration
3. Complete extraction rewrite

## Time Estimates
- **Phase 1-2**: 15 minutes - Should identify most common issues
- **Phase 3-4**: 25 minutes - Comprehensive library and environment testing  
- **Phase 5-6**: 30 minutes - Alternative approaches and fixes

**Total**: 70 minutes maximum for complete debugging and resolution
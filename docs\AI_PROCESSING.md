# AI Document Processing Documentation

## Overview

Bidaible's AI-powered document processing system automatically extracts structured data from construction RFQ documents using a sophisticated multi-provider approach. The system is designed for reliability, accuracy, and comprehensive data extraction.

## Architecture

### Multi-Provider AI System
- **Primary Engine**: Configurable via `PRIMARY_MODEL` environment variable (default: Groq)
- **Groq**: moonshotai/kimi-k2-instruct (Ultra-fast inference, sub-3-second analysis)
- **Google Gemini**: 2.5 Pro (High accuracy, excellent reasoning, reliable fallback)
- **OpenAI**: GPT-4.1-mini (Fast, reliable, structured output)
- **Intelligent Fallback**: Automatic failover chain (Groq → Gemini → OpenAI → Manual) ensuring 100% reliability

### Enhanced Processing Pipeline

```
Multi-File Upload → File Classification → Priority Processing → AI Analysis → Comprehensive Summary → Database Storage
       ↓                    ↓                      ↓               ↓                  ↓
  (Up to 8 files)    (Main/Supporting)    (Main File Focus)  (Construction AI)  (Rich Summaries)
                                                                 ↓
Bid Submission → Bid Analysis → AI Bid Evaluation → Competitive Scoring → Dashboard Display
```

### Main File Priority Processing
1. **Priority Detection**: System identifies main RFQ files for comprehensive processing
2. **Comprehensive Extraction**: Main files receive full AI analysis with detailed summaries
3. **Supporting File Integration**: Drawings, specs, and addenda contribute supplemental requirements
4. **Intelligent Merging**: Priority-based data consolidation with main file precedence

## Supported File Types

| Format | Extension | Processing Method | Max Size | Multi-File Support |
|--------|-----------|-------------------|----------|-------------------|
| PDF | `.pdf` | PDF.js + Enhanced AI | 250MB | ✅ Up to 8 files |
| Text Files | `.txt` | Direct + Enhanced AI | 250MB | ✅ Up to 8 files |
| CSV | `.csv` | Direct + Enhanced AI | 250MB | ✅ Up to 8 files |

### File Type Classification
- **Main RFQ Files**: Primary documents with comprehensive AI processing
- **Drawings**: Supporting technical drawings and plans
- **Specifications**: Technical specifications and requirements
- **Addendum**: Amendments and additional information
- **Supporting**: General supporting documentation

## Text Extraction Methods

### PDF Processing
1. **PDF.js Library**: Extracts clean text content from PDF documents
2. **Character Limit**: Successfully processes 24,000+ character documents
3. **Page-by-page**: Processes large documents efficiently
4. **Clean Output**: Removes formatting artifacts and provides pure text

### Office Document Processing
1. **Text Extraction**: Processes document content for readable text
2. **Character Cleaning**: Removes non-printable characters and normalizes whitespace
3. **Fallback Processing**: Multiple extraction methods for reliability

## Enhanced AI Processing

### Construction-Specific AI Analysis
The system now features a professional construction project analyst with industry-specific capabilities:

#### Construction Industry Intelligence
- **Trade Category Analysis**: Automated identification of construction trades and specialties
- **Project Complexity Assessment**: Evaluation of project scope, phases, and complexity levels
- **Risk Factor Analysis**: Professional assessment of potential challenges and opportunities
- **Timeline Intelligence**: Project milestones, completion requirements, and critical path analysis

#### Enhanced AI Prompts
```typescript
const CONSTRUCTION_AI_PROMPT = {
  role: "Expert Construction Project Analyst",
  capabilities: [
    "construction_scope_analysis",
    "trade_category_identification", 
    "project_complexity_assessment",
    "professional_insights",
    "comprehensive_summary_generation"
  ],
  output_format: "structured_json_with_markdown_summary",
  token_limit: 3000 // Enhanced for comprehensive summaries
};
```

### Multi-Provider System with Enhanced Processing
The system supports three AI providers with intelligent fallback and construction-specific processing:

```typescript
const AI_PROVIDERS = {
  openai: {
    model: "gpt-4.1-mini",
    endpoint: OpenAI API,
    features: ["structured_output", "construction_analysis", "comprehensive_summaries"]
  },
  gemini: {
    model: "gemini-2.5-pro",
    endpoint: Google AI API,
    features: ["high_accuracy", "reasoning", "construction_intelligence"]
  },
  groq: {
    model: "moonshotai/kimi-k2-instruct",
    endpoint: Groq API,
    features: ["ultra_fast", "bid_analysis", "construction_specific"]
  }
};
```

### Comprehensive AI Summary Generation
Each main RFQ file receives a professional markdown-formatted summary including:

1. **Project Overview**: Complete project details, location, and timeline
2. **Scope of Work**: Detailed construction scope and trade analysis
3. **Key Requirements**: Critical specifications and qualifications
4. **Submission Requirements**: Bid submission details and deadlines
5. **Timeline & Milestones**: Project phases and completion requirements
6. **Contact Information**: Primary contacts and organization details
7. **Professional Analysis**: Complexity assessment, risks, and opportunities

## AI Bid Analysis System

### Overview
The AI Bid Analysis system provides comprehensive competitive intelligence and bid evaluation capabilities through an advanced three-tab dashboard interface.

### Key Features
- **Executive Summary Generation**: AI-powered insights with key findings, recommendations, and risk factors
- **Competitive Bid Ranking**: Automated scoring with detailed reasoning and market positioning
- **Market Analysis**: Price spreads, competitive assessment, and strategic recommendations
- **Real-time Generation**: Sub-3-second analysis powered by Groq Kimi K2 model
- **Comprehensive Fallback**: 100% reliability through multi-provider architecture

### Bid Analysis Pipeline
```
Bid Submissions → Data Aggregation → AI Analysis → Competitive Scoring → Dashboard Display
                                        ↓
                      Executive Summary + Bid Ranking + Market Analysis
```

### AI Analysis Components

#### 1. Executive Summary
- **Overview**: Comprehensive bidding landscape assessment
- **Key Insights**: Critical findings and competitive advantages
- **Recommendations**: Actionable bid evaluation guidance
- **Risk Factors**: Potential issues and mitigation strategies

#### 2. Bid Ranking
- **AI Scoring**: Automated competitive scoring (0-100 scale)
- **Detailed Reasoning**: Explanation for each bid's score
- **Risk Assessment**: Low/Medium/High risk classification
- **Competitive Position**: Market positioning analysis

#### 3. Market Analysis
- **Price Spreads**: Min/Max/Average/Median bid analysis
- **Competitive Positioning**: Market landscape assessment
- **Risk Assessment**: Overall project risk evaluation

### System Prompts
Specialized prompts optimized for RFQ document analysis and bid evaluation:

#### RFQ Document Analysis
```
You are an expert AI assistant specializing in construction RFQ document analysis.
Extract comprehensive project information in valid JSON format including:
- Project details and specifications
- Contact information
- Timeline and deadlines
- Requirements and evaluation criteria
- Comprehensive project summaries
```

### Data Validation
- **JSON Schema**: Ensures valid output format
- **Field Validation**: Type checking and format validation
- **Error Handling**: Graceful degradation with fallback values
- **Multi-Provider Retry**: Automatic retry with different AI providers

## Extracted Data Fields

### Core Project Information
- **fileName**: Original document filename
- **projectDescription**: Comprehensive project description including scope, objectives, and details
- **projectSummary**: Executive summary or overview of the project
- **projectName**: Extracted project name
- **projectLocation**: Full project address and location details

### Contact Information
- **contactName**: Primary contact person for the RFQ
- **contactEmail**: Email address for communications and submissions
- **owner**: Project owner or client organization

### Requirements & Timeline
- **requirements**: Array of strings containing project requirements, specifications, qualifications needed
- **finalAwardDate**: Project award deadline or final submission date
- **scope**: Detailed scope of work and deliverables

### AI-Generated Summary
- **aiSummary**: Comprehensive markdown-formatted summary including:
  - Project overview and key details
  - Timeline and important dates
  - Scope of work highlights
  - Submission requirements
  - Evaluation criteria

## Data Mapping & Storage

### Database Schema Mapping
```typescript
// AI Extracted Data → Database Fields
{
  projectName: extractedData.projectName || extractedData.fileName || "Untitled Project",
  projectLocation: extractLocation(extractedData.projectLocation),
  description: extractedData.projectDescription || extractedData.projectSummary || "No description available",
  dueDate: parseDate(extractedData.finalAwardDate) || defaultDate,
  tradeCategory: "general", // Default value
  status: "Draft",
  extractedData: extractedData, // Full AI output
  aiSummary: extractedData.aiSummary // Markdown summary
}
```

### Location Extraction
Advanced location parsing that handles various address formats:
- Full addresses with city, state, zip
- Partial addresses with cross-streets
- Project site descriptions
- Fallback to basic text extraction

### Text Processing
1. **Character Cleaning**: Removes null bytes and control characters
2. **Encoding Safety**: Prevents database encoding errors
3. **Length Validation**: Ensures meaningful content extraction
4. **Whitespace Normalization**: Standardizes spacing and formatting

## Performance Metrics

### Success Rates
- **PDF Extraction**: 95%+ success rate with PDF.js
- **AI Processing**: 90%+ accurate field extraction with multi-provider fallback
- **Data Mapping**: 100% schema compliance

### Processing Times
- **Small Documents** (<5 pages): 2-5 seconds
- **Medium Documents** (5-50 pages): 5-15 seconds  
- **Large Documents** (50+ pages): 15-30 seconds

### Character Limits
- **Maximum Text**: 50,000 characters per document
- **Tested Capacity**: Successfully processed 24,000+ character documents
- **Truncation**: Automatic truncation for oversized content

## Error Handling

### Fallback Chain
1. **Primary Provider**: Configured via PRIMARY_MODEL environment variable
2. **Secondary Fallback**: Next available AI provider in chain
3. **Tertiary Fallback**: Remaining AI provider
4. **Graceful Degradation**: Basic text extraction if all AI providers fail

### Common Issues & Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| AI Provider Timeout | Network/API issues | Automatic retry with next provider |
| Invalid JSON Response | Model output issues | Retry with different provider |
| Text Extraction Failure | Corrupted/complex files | Multiple extraction methods |
| Character Encoding | Special characters | Enhanced cleaning pipeline |

## Configuration

### Environment Variables
```env
# AI Model Selection
PRIMARY_MODEL=openai          # 'openai', 'gemini', or 'groq'

# API Keys (At least one required)
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
GROQ_API_KEY=your_groq_key

# Processing Limits
MAX_FILE_SIZE=52428800        # 50MB in bytes
MAX_TEXT_LENGTH=50000         # Character limit
```

### Model Configuration
```typescript
// Primary model with intelligent fallback
const PRIMARY_MODEL = process.env.PRIMARY_MODEL || "openai";

// Model specifications
const MODELS = {
  openai: "gpt-4o-mini",
  gemini: "gemini-2.0-flash-exp", 
  groq: "llama-3.3-70b-versatile"
};
```

## Monitoring & Debugging

### Logging
- **File Processing**: Detailed logs for each document
- **AI Requests**: Request/response logging with timing and provider used
- **Error Tracking**: Comprehensive error reporting with fallback triggers
- **Character Counts**: Text extraction metrics and processing duration

### Debug Mode
Enable detailed debugging by checking server logs:
```bash
# View processing logs
npm run dev

# Check for AI extraction details
console.log("Primary provider:", providerUsed);
console.log("Processing time:", duration);
console.log("Extracted data:", result.structuredData);
```

## Best Practices

### Document Preparation
1. **Clean PDFs**: Use text-based PDFs rather than scanned images
2. **Clear Structure**: Well-formatted documents extract better
3. **Standard Formats**: Use common RFQ templates when possible

### Upload Guidelines
1. **File Size**: Keep under 50MB for optimal performance
2. **File Quality**: Higher quality documents = better extraction
3. **Multiple Files**: System can process multiple documents per RFQ

### Validation
1. **Review Extracted Data**: Always verify AI-extracted information
2. **Manual Override**: Edit extracted data if needed
3. **Requirements Check**: Ensure all critical information is captured

## Recent Enhancements

### Latest Updates
- **Multi-Provider Support**: Added Groq Llama for ultra-fast processing
- **Enhanced Location Parsing**: Improved address extraction accuracy
- **AI Summary Generation**: Comprehensive markdown summaries
- **Error Recovery**: Better fallback mechanisms across providers
- **Performance Optimization**: Faster processing with intelligent provider selection

### Future Enhancements
- **OCR Integration**: For scanned document processing
- **Batch Processing**: Multiple document processing
- **Template Learning**: Improved extraction for common formats
- **Confidence Scoring**: Reliability metrics for extracted data
- **Custom Model Training**: Industry-specific model fine-tuning

## Support

For AI processing issues:
- Check API key configuration
- Verify network connectivity
- Review file format compatibility
- Monitor processing logs for errors
- Test with different AI providers

---

Built with multiple AI providers for maximum reliability and performance.
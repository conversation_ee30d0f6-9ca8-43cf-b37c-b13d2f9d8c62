import { User, MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { NotificationDropdown } from "@/components/NotificationDropdown";
import { UserFeedbackModal } from "@/components/UserFeedbackModal";
import { useAuth } from "@/hooks/useAuth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ProfileModal } from "@/components/ProfileModal";
import { useState } from "react";
import { useLocation } from "wouter";
import bidaibleLogo from "@assets/logo_1752612470296.png";

export function Navbar() {
  const { user, isAuthenticated } = useAuth();
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [, navigate] = useLocation();

  const handleProfileClick = () => {
    setShowProfileModal(true);
  };

  const handleSettingsClick = () => {
    navigate("/settings");
  };

  return (
    <nav className="bg-card border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <img 
              src={bidaibleLogo} 
              alt="Bidaible Logo" 
              className="w-16 h-16 object-contain"
            />
            <span className="text-xl font-semibold text-foreground">Bidaible</span>
          </div>
          

          
          {/* User Actions */}
          <div className="flex items-center space-x-4">
            {/* User Feedback Button */}
            {isAuthenticated && (
              <Button 
                size="sm" 
                onClick={() => setShowFeedbackModal(true)}
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                User Feedback
              </Button>
            )}
            
            <ThemeToggle />
            
            {/* Authenticated user features */}
            {isAuthenticated && (
              <>
                {/* Notifications */}
                <NotificationDropdown />
                
                {/* User Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={(user as any)?.profileImageUrl || ""} />
                        <AvatarFallback>
                          {(user as any)?.firstName?.[0]}{(user as any)?.lastName?.[0]}
                        </AvatarFallback>
                      </Avatar>
                      <span className="hidden md:block text-sm font-medium">
                        {(user as any)?.firstName} {(user as any)?.lastName}
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleProfileClick}>Profile</DropdownMenuItem>
                    <DropdownMenuItem onClick={handleSettingsClick}>Settings</DropdownMenuItem>
                    <DropdownMenuItem>
                      <a href="/api/logout" className="w-full">Logout</a>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* Profile Modal */}
      <ProfileModal 
        open={showProfileModal} 
        onOpenChange={setShowProfileModal}
        user={user}
      />
      
      {/* User Feedback Modal */}
      <UserFeedbackModal
        open={showFeedbackModal}
        onOpenChange={setShowFeedbackModal}
      />
    </nav>
  );
}

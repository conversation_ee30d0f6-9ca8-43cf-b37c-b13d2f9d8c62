import * as pdfjsLib from "pdfjs-dist/legacy/build/pdf.mjs";
import * as fs from "fs";
import { createReadStream } from "fs";
import { pipeline } from "stream/promises";
import { Transform } from "stream";

interface ProcessingOptions {
  chunkSize?: number;
  timeout?: number;
  maxConcurrent?: number;
  onProgress?: (progress: { stage: string; percentage: number; message: string }) => void;
}

interface StreamProcessingResult {
  extractedText: string;
  processingTime: number;
  chunks: number;
  success: boolean;
  error?: string;
}

export class AIStreamProcessor {
  private readonly MAX_CONCURRENT = 8;
  private readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private readonly CHUNK_SIZE = 1024 * 1024; // 1MB chunks
  private activeProcessors = new Set<string>();

  /**
   * Process PDF file using stream-based approach
   */
  async processPDFStream(
    filePath: string, 
    options: ProcessingOptions = {}
  ): Promise<StreamProcessingResult> {
    const startTime = Date.now();
    const processingId = `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Check concurrent processing limit
      if (this.activeProcessors.size >= (options.maxConcurrent || this.MAX_CONCURRENT)) {
        throw new Error('Maximum concurrent processing limit reached');
      }

      this.activeProcessors.add(processingId);
      options.onProgress?.({ stage: 'pdf_loading', percentage: 10, message: 'Loading PDF document' });

      // Stream-based PDF processing
      const pdfBuffer = await this.readFileInChunks(filePath, options);
      options.onProgress?.({ stage: 'pdf_parsing', percentage: 30, message: 'Parsing PDF structure' });

      // Convert Buffer to Uint8Array for PDF.js compatibility
      const pdfData = new Uint8Array(pdfBuffer);
      const pdfDoc = await pdfjsLib.getDocument({ data: pdfData }).promise;
      const numPages = pdfDoc.numPages;
      
      let extractedText = '';
      let processedChunks = 0;

      // Process pages in batches to manage memory
      const batchSize = 5; // Process 5 pages at a time
      for (let i = 1; i <= numPages; i += batchSize) {
        const endPage = Math.min(i + batchSize - 1, numPages);
        const batch = await this.processPDFBatch(pdfDoc, i, endPage);
        extractedText += batch;
        processedChunks++;

        const percentage = 30 + (70 * (endPage / numPages));
        options.onProgress?.({ 
          stage: 'text_extraction', 
          percentage: Math.round(percentage), 
          message: `Extracted text from pages ${i}-${endPage}` 
        });

        // Yield control to prevent blocking
        await new Promise(resolve => setImmediate(resolve));
      }

      const processingTime = Date.now() - startTime;
      options.onProgress?.({ stage: 'complete', percentage: 100, message: 'PDF processing complete' });

      return {
        extractedText: extractedText.trim(),
        processingTime,
        chunks: processedChunks,
        success: true
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`PDF stream processing failed for ${processingId}:`, error);
      
      return {
        extractedText: '',
        processingTime,
        chunks: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      this.activeProcessors.delete(processingId);
    }
  }

  /**
   * Process text file using stream approach
   */
  async processTextStream(
    filePath: string,
    options: ProcessingOptions = {}
  ): Promise<StreamProcessingResult> {
    const startTime = Date.now();
    const processingId = `text_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      if (this.activeProcessors.size >= (options.maxConcurrent || this.MAX_CONCURRENT)) {
        throw new Error('Maximum concurrent processing limit reached');
      }

      this.activeProcessors.add(processingId);
      options.onProgress?.({ stage: 'text_loading', percentage: 20, message: 'Loading text file' });

      let extractedText = '';
      let chunks = 0;

      const textStream = createReadStream(filePath, { 
        encoding: 'utf8',
        highWaterMark: options.chunkSize || this.CHUNK_SIZE 
      });

      const textProcessor = new Transform({
        transform(chunk, encoding, callback) {
          extractedText += chunk.toString();
          chunks++;
          callback();
        }
      });

      await pipeline(textStream, textProcessor);
      
      const processingTime = Date.now() - startTime;
      options.onProgress?.({ stage: 'complete', percentage: 100, message: 'Text processing complete' });

      return {
        extractedText: extractedText.trim(),
        processingTime,
        chunks,
        success: true
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`Text stream processing failed for ${processingId}:`, error);
      
      return {
        extractedText: '',
        processingTime,
        chunks: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      this.activeProcessors.delete(processingId);
    }
  }

  /**
   * Read file in chunks to manage memory usage
   */
  private async readFileInChunks(
    filePath: string, 
    options: ProcessingOptions
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      const stream = createReadStream(filePath, { 
        highWaterMark: options.chunkSize || this.CHUNK_SIZE 
      });

      const timeoutId = setTimeout(() => {
        stream.destroy();
        reject(new Error('File reading timeout'));
      }, options.timeout || this.DEFAULT_TIMEOUT);

      stream.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      stream.on('end', () => {
        clearTimeout(timeoutId);
        resolve(Buffer.concat(chunks));
      });

      stream.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }

  /**
   * Process a batch of PDF pages
   */
  private async processPDFBatch(
    pdfDoc: any, 
    startPage: number, 
    endPage: number
  ): Promise<string> {
    let batchText = '';
    
    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
      try {
        const page = await pdfDoc.getPage(pageNum);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        batchText += `\n\n--- Page ${pageNum} ---\n${pageText}`;
      } catch (pageError) {
        console.warn(`Error processing page ${pageNum}:`, pageError);
        batchText += `\n\n--- Page ${pageNum} (Error) ---\n`;
      }
    }
    
    return batchText;
  }

  /**
   * Get current processing statistics
   */
  getProcessingStats() {
    return {
      activeProcessors: this.activeProcessors.size,
      maxConcurrent: this.MAX_CONCURRENT,
      availableSlots: this.MAX_CONCURRENT - this.activeProcessors.size
    };
  }

  /**
   * Clean up resources and cancel active processing
   */
  async cleanup(): Promise<void> {
    console.log(`Cleaning up ${this.activeProcessors.size} active processors`);
    this.activeProcessors.clear();
  }
}

// Singleton instance
export const streamProcessor = new AIStreamProcessor();
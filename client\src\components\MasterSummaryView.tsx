
import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, FileWarning, CheckCircle2, AlertTriangle, Info } from "lucide-react";
import { queryClient } from "@/lib/queryClient";

interface ConflictFlag {
  type: 'date' | 'amount' | 'requirement' | 'specification';
  field: string;
  documents: string[];
  values: any[];
  severity: 'low' | 'medium' | 'high';
}

interface MasterSummaryViewProps {
  rfqId: string;
}

export function MasterSummaryView({ rfqId }: MasterSummaryViewProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  
  const { data: rfq } = useQuery({
    queryKey: [`/api/rfqs/${rfqId}`],
  });
  
  const generateMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/rfqs/${rfqId}/generate-master-summary`, {
        method: 'POST',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to generate summary');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/rfqs/${rfqId}`] });
      setIsGenerating(false);
    },
    onError: () => {
      setIsGenerating(false);
    }
  });
  
  const handleGenerate = () => {
    setIsGenerating(true);
    generateMutation.mutate();
  };
  
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium':
        return <FileWarning className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };
  
  const getSeverityVariant = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'default';
      default:
        return 'secondary';
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Master Summary</span>
          {rfq?.summaryGeneratedAt && (
            <Badge variant="outline" className="text-xs">
              Last updated: {new Date(rfq.summaryGeneratedAt).toLocaleDateString()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {rfq?.masterSummary ? (
          <div className="space-y-6">
            {/* Conflicts Section */}
            {rfq.conflictFlags && rfq.conflictFlags.length > 0 && (
              <div className="space-y-4">
                <Alert variant="warning">
                  <FileWarning className="h-4 w-4" />
                  <AlertDescription>
                    {rfq.conflictFlags.length} conflict{rfq.conflictFlags.length > 1 ? 's' : ''} detected between documents. Review carefully before proceeding.
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold">Conflict Details:</h4>
                  {rfq.conflictFlags.map((conflict: ConflictFlag, index: number) => (
                    <div key={index} className="border rounded-lg p-3 bg-muted/30">
                      <div className="flex items-center gap-2 mb-2">
                        {getSeverityIcon(conflict.severity)}
                        <Badge variant={getSeverityVariant(conflict.severity)}>
                          {conflict.severity.toUpperCase()}
                        </Badge>
                        <span className="text-sm font-medium capitalize">
                          {conflict.type} conflict in {conflict.field}
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p className="mb-1">Conflicting values:</p>
                        <ul className="list-disc list-inside space-y-1">
                          {conflict.values.map((value, idx) => (
                            <li key={idx}>
                              <strong>{conflict.documents[idx] || 'Unknown document'}:</strong> {value}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Master Summary Content */}
            <div className="prose dark:prose-invert max-w-none">
              <div dangerouslySetInnerHTML={{ __html: rfq.masterSummary }} />
            </div>
            
            <div className="flex gap-2">
              <Button onClick={handleGenerate} variant="outline" disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Regenerating...
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Regenerate Summary
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <FileWarning className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No Master Summary Available</h3>
            <p className="text-muted-foreground mb-4">
              Generate a comprehensive summary that consolidates information from all uploaded documents and detects conflicts.
            </p>
            <Button onClick={handleGenerate} disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Master Summary'
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

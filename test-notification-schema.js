// Test script for notification database schema
import { db } from './server/db.js';
import { 
  notifications, 
  notificationPreferences, 
  notificationDeliveries 
} from './shared/schema.js';

async function testNotificationSchema() {
  console.log('🔧 Testing notification database schema...');
  
  try {
    // Test 1: Create a test notification
    console.log('📝 Creating test notification...');
    const testNotification = await db.insert(notifications).values({
      userId: 'test-user-id',
      type: 'rfq_uploaded',
      title: 'Test RFQ Notification',
      message: 'This is a test notification for RFQ upload',
      priority: 'medium',
      data: { rfqId: 'test-rfq-123', projectName: 'Test Project' }
    }).returning();
    
    console.log('✅ Notification created:', testNotification[0]);
    
    // Test 2: Create notification preferences
    console.log('📝 Creating notification preferences...');
    const testPreferences = await db.insert(notificationPreferences).values({
      userId: 'test-user-id',
      type: 'rfq_uploaded',
      emailEnabled: true,
      inAppEnabled: true,
      frequency: 'immediate'
    }).returning();
    
    console.log('✅ Preferences created:', testPreferences[0]);
    
    // Test 3: Create delivery record
    console.log('📝 Creating delivery record...');
    const testDelivery = await db.insert(notificationDeliveries).values({
      notificationId: testNotification[0].id,
      deliveryMethod: 'email',
      recipient: '<EMAIL>',
      status: 'delivered'
    }).returning();
    
    console.log('✅ Delivery record created:', testDelivery[0]);
    
    // Test 4: Query notifications
    console.log('🔍 Querying notifications...');
    const allNotifications = await db.select().from(notifications).limit(5);
    console.log('✅ Found notifications:', allNotifications.length);
    
    // Clean up test data
    console.log('🧹 Cleaning up test data...');
    await db.delete(notificationDeliveries).where(eq(notificationDeliveries.notificationId, testNotification[0].id));
    await db.delete(notificationPreferences).where(eq(notificationPreferences.id, testPreferences[0].id));
    await db.delete(notifications).where(eq(notifications.id, testNotification[0].id));
    
    console.log('🎉 All tests passed! Notification schema is working correctly.');
    return true;
    
  } catch (error) {
    console.error('❌ Notification schema test failed:', error);
    return false;
  }
}

// Import eq function
const { eq } = await import('drizzle-orm');

// Run the test
testNotificationSchema()
  .then(success => {
    console.log('Test result:', success ? 'SUCCESS' : 'FAILED');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
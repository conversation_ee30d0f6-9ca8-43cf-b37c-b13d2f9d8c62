
# Frontend Documentation

## Overview

The Bidaible frontend is built with React 18, TypeScript, and modern tooling to provide a responsive, accessible, and performant user experience for construction bidding management.

## Architecture

### Technology Stack
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side navigation
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query for server state, React Context for global state
- **Build Tool**: Vite with hot module replacement
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React icons

### Project Structure
```
client/
├── src/
│   ├── components/          # Reusable components
│   │   ├── ui/             # Base UI components (shadcn/ui)
│   │   └── *.tsx           # Business logic components
│   ├── pages/              # Page components
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility functions and configuration
│   └── assets/             # Static assets
└── public/                 # Public assets
```

## Core Features

### Authentication System
- **Provider**: Replit Auth with OAuth integration
- **Session Management**: Automatic token refresh and validation
- **Role-Based Access**: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Editor, Viewer roles
- **Route Protection**: Automatic redirection for unauthorized access

### Dashboard
- **Main Dashboard**: Central hub with embedded Bid Management Dashboard
- **Statistics Widget**: Real-time metrics and performance indicators
- **Quick Actions**: Prominent access to core functionality
- **Responsive Design**: Optimized for desktop and mobile

### RFQ Management
- **Creation Flow**: Multi-step RFQ creation with document upload
- **Detail View**: Comprehensive RFQ information with tabbed interface
- **AI Integration**: Automatic document processing and data extraction
- **Status Tracking**: Real-time status updates and deadline monitoring

### Bid Management
- **Submission Interface**: Streamlined bid submission for contractors
- **Analysis Dashboard**: AI-powered bid analysis with competitive intelligence
- **Comparison Tools**: Side-by-side bid comparison with visual indicators
- **Response Tracking**: Real-time bid response monitoring

### Document Processing
- **Upload Interface**: Drag-and-drop file upload with progress tracking
- **AI Extraction**: Automatic data extraction from PDF, TXT, CSV files
- **Document Viewer**: Enhanced PDF viewing with new-tab opening
- **Storage Integration**: Replit Object Storage for secure file management

## Component Architecture

### Base UI Components (`components/ui/`)

#### Design System
All UI components follow shadcn/ui design principles:
- Consistent spacing and typography
- Accessible by default
- Customizable through Tailwind CSS
- TypeScript support

#### Key Components
- **Button**: Multiple variants (default, destructive, outline, secondary, ghost, link)
- **Card**: Container component for grouped content
- **Tabs**: Tabbed interface for organizing content
- **Dialog**: Modal dialogs for focused interactions
- **Form**: Form components with validation support
- **Table**: Data tables with sorting and filtering

### Business Components

#### RfqDetailView
```typescript
// File: client/src/components/RfqDetailView.tsx
interface RfqDetailViewProps {
  rfqId: string;
}
```
**Features**:
- Tabbed interface with 7 tabs for RFQ owners
- Role-based tab visibility
- Real-time data updates
- Document management integration
- AI analysis integration

#### BidAnalyticsDashboard
```typescript
// File: client/src/components/BidAnalyticsDashboard.tsx
interface BidAnalyticsDashboardProps {
  rfqId: string;
}
```
**Features**:
- AI-powered analysis with Groq integration
- Executive summary generation
- Competitive scoring and ranking
- Risk assessment visualization
- Real-time processing indicators

#### MasterSummaryView
```typescript
// File: client/src/components/MasterSummaryView.tsx
interface MasterSummaryViewProps {
  rfqId: string;
}
```
**Features**:
- Document consolidation
- Conflict detection and visualization
- Summary regeneration
- Last updated tracking

### Navigation Components

#### Sidebar
**Features**:
- Role-based menu items
- Active page highlighting
- Collapsible design
- Quick action shortcuts

#### Navbar
**Features**:
- User authentication status
- Profile dropdown with role display
- Notification center
- Theme toggle

## State Management

### Server State (TanStack Query)
```typescript
// Query configuration
const { data: rfqs, isLoading, error } = useQuery({
  queryKey: ['/api/rfqs'],
  queryFn: async () => {
    const response = await fetch('/api/rfqs', {
      credentials: 'include'
    });
    if (!response.ok) throw new Error('Failed to fetch RFQs');
    return response.json();
  }
});
```

**Benefits**:
- Automatic caching and background updates
- Optimistic mutations
- Error handling and retry logic
- Loading state management

### Form State (React Hook Form)
```typescript
// Form with validation
const form = useForm<FormData>({
  resolver: zodResolver(formSchema),
  defaultValues: {
    projectName: '',
    projectLocation: '',
    dueDate: ''
  }
});
```

**Benefits**:
- Type-safe form validation
- Performance optimization
- Built-in error handling
- Integration with UI components

### Global State (React Context)
```typescript
// Theme context
const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  setTheme: () => {}
});
```

**Use Cases**:
- Theme management
- User authentication state
- Global notifications
- Application-wide settings

## Analytics Components

### Contractor Performance Analytics
The `ContractorPerformanceAnalytics` component provides comprehensive performance tracking for contractors:

```typescript
interface ContractorStats {
  totalBidsSubmitted: number;
  totalBidsWon: number;
  winRate: number;
  averageBidAmount: number;
  totalEarnings: number;
  monthlyActivity: Array<{
    month: string;
    bidsSubmitted: number;
    bidsWon: number;
    earnings: number;
  }>;
  bidsByStatus: Array<{
    status: string;
    count: number;
    color: string;
  }>;
  latestBid?: {
    amount: number;
    status: string;
    submittedAt: string;
  };
}
```

**Key Features**:
- **Real-time Metrics**: Calculates average bid amount from ALL submitted bids (not just won bids)
- **Historical Tracking**: 6-month activity trends with interactive Recharts visualizations
- **Status Distribution**: Color-coded pie charts showing bid status breakdown
- **Recent Activity**: Latest bid information with actual amounts and status badges
- **Performance Insights**: Win rates, earnings tracking, and trend analysis

**Recent Fixes (July 28, 2025)**:
- Fixed backend calculation to use correct bid schema properties (`bidAmount`/`extractedAmount`)
- Corrected average calculation logic to include ALL submitted bids for accurate metrics
- Enhanced Recent Bid Activity section with actual bid values and dynamic status badges
- Resolved TypeScript compilation errors related to bid property access

## Styling System

### Tailwind CSS Configuration
```typescript
// tailwind.config.ts
export default {
  content: ["./src/**/*.{ts,tsx}"],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))"
      }
    }
  }
}
```

### Design Tokens
- **Colors**: Semantic color system with light/dark mode support
- **Typography**: Plus Jakarta Sans font family
- **Spacing**: Consistent spacing scale
- **Breakpoints**: Mobile-first responsive design

### Component Styling
```typescript
// Example: Button component variants
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent"
      }
    }
  }
);
```

## Performance Optimization

### Code Splitting
```typescript
// Lazy loading for large components
const BidAnalyticsDashboard = lazy(() => import('./components/BidAnalyticsDashboard'));

// Usage with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <BidAnalyticsDashboard rfqId={rfqId} />
</Suspense>
```

### Memoization
```typescript
// Expensive computations
const expensiveValue = useMemo(() => {
  return computeComplexAnalysis(bids);
}, [bids]);

// Event handlers
const handleSubmit = useCallback((data: FormData) => {
  submitForm(data);
}, [submitForm]);
```

### Virtual Scrolling
For large lists (contractor lists, bid lists):
```typescript
import { FixedSizeList as List } from 'react-window';

<List
  height={600}
  itemCount={contractors.length}
  itemSize={80}
  itemData={contractors}
>
  {ContractorRow}
</List>
```

## Accessibility

### ARIA Support
```typescript
// Proper ARIA labels and roles
<button
  aria-label="Submit bid"
  aria-describedby="bid-help-text"
  role="button"
>
  Submit
</button>
```

### Keyboard Navigation
- Tab order management
- Focus indicators
- Keyboard shortcuts
- Screen reader support

### Color Contrast
- WCAG AA compliance
- High contrast mode support
- Color-blind friendly palette

## Testing Strategy

### Unit Testing (Jest + React Testing Library)
```typescript
// Component testing
import { render, screen } from '@testing-library/react';
import { BidAnalyticsDashboard } from './BidAnalyticsDashboard';

test('renders bid analysis dashboard', () => {
  render(<BidAnalyticsDashboard rfqId="test-rfq" />);
  expect(screen.getByText(/executive summary/i)).toBeInTheDocument();
});
```

### Integration Testing
```typescript
// API integration testing
test('submits bid successfully', async () => {
  const user = userEvent.setup();
  render(<BidSubmissionForm rfqId="test-rfq" />);
  
  await user.type(screen.getByLabelText(/bid amount/i), '100000');
  await user.click(screen.getByRole('button', { name: /submit/i }));
  
  expect(await screen.findByText(/bid submitted/i)).toBeInTheDocument();
});
```

### End-to-End Testing (Cypress)
```typescript
// E2E workflow testing
describe('RFQ Creation Flow', () => {
  it('creates an RFQ successfully', () => {
    cy.visit('/rfqs/create');
    cy.get('[data-cy=project-name]').type('Test Project');
    cy.get('[data-cy=submit]').click();
    cy.url().should('include', '/rfqs/');
  });
});
```

## Error Handling

### Error Boundaries
```typescript
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

### API Error Handling
```typescript
// Centralized error handling
const handleApiError = (error: unknown) => {
  if (error instanceof Error) {
    toast.error(error.message);
  } else {
    toast.error('An unexpected error occurred');
  }
};
```

## Build and Development

### Development Server
```bash
npm run dev
# Starts Vite dev server with HMR on port 5173
```

### Build Process
```bash
npm run build
# Creates optimized production build in dist/
```

### Environment Variables
```env
# Development
VITE_API_URL=http://localhost:5000
VITE_GEMINI_API_KEY=your_key_here
```

## Deployment

### Production Build
- Static asset optimization
- Code splitting
- Tree shaking
- Minification

### Replit Deployment
- Automatic deployment on push
- Environment variable management
- Custom domain support
- SSL certificate handling

## Best Practices

### Component Development
- Single responsibility principle
- Props interface documentation
- Error boundary wrapping
- Performance optimization

### Code Organization
- Feature-based folder structure
- Consistent naming conventions
- Import path optimization
- TypeScript strict mode

### User Experience
- Loading states for all async operations
- Optimistic updates where appropriate
- Error messaging that guides user action
- Responsive design for all screen sizes

## Future Enhancements

### Planned Features
- Real-time collaboration
- Advanced data visualization
- Mobile app development
- Offline support

### Performance Improvements
- Service worker implementation
- Bundle size optimization
- Image optimization
- CDN integration

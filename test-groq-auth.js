
import OpenAI from "openai";

async function testGroqAuth() {
  console.log("🔍 Testing Groq API Authentication...");
  
  const apiKey = process.env.GROQ_API_KEY;
  console.log("API Key present:", !!apiKey);
  console.log("API Key length:", apiKey ? apiKey.length : 0);
  
  if (!apiKey) {
    console.error("❌ GROQ_API_KEY not found in environment");
    console.log("💡 Make sure to set GROQ_API_KEY in your Secrets");
    return;
  }
  
  const groq = new OpenAI({ 
    apiKey: apiKey,
    baseURL: "https://api.groq.com/openai/v1"
  });
  
  try {
    const response = await groq.chat.completions.create({
      model: "llama3-8b-8192",
      messages: [
        {
          role: "user",
          content: "Say 'Authentication successful' if you can read this."
        }
      ],
      max_tokens: 20,
      temperature: 0.1
    });

    console.log("✅ Groq API Authentication SUCCESSFUL!");
    console.log("Response:", response.choices[0].message.content);
    
  } catch (error) {
    console.error("❌ Groq API Authentication FAILED:");
    console.error("Status:", error.status);
    console.error("Message:", error.message);
  }
}

testGroqAuth();

import { Request, Response, NextFunction } from 'express';
import { verifyApiKey, recordFailedAttempt, getKeySecurityInfo } from '../services/apiKeyService';
import { storage } from '../storage';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export async function apiAuthentication(req: any, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ 
      message: 'API key required. Include "Authorization: Bearer your-api-key" header.' 
    });
  }
  
  const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
  
  try {
    // Get client IP for security checks
    const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] as string;
    
    const keyData = await verify<PERSON><PERSON><PERSON><PERSON>(apiKey, clientIP);
    
    if (!keyData) {
      return res.status(401).json({ 
        message: 'Invalid or expired API key.' 
      });
    }

    // Check if key is locked
    const securityInfo = getKeySecurityInfo(keyData.id);
    if (securityInfo.isLocked) {
      return res.status(423).json({
        message: 'API key is temporarily locked due to security concerns. Please contact support.',
        lockoutUntil: securityInfo.lockoutUntil
      });
    }
    
    // Check rate limit
    const now = Date.now();
    const hourInMs = 60 * 60 * 1000;
    const rateLimitKey = keyData.id;
    
    let rateLimitData = rateLimitStore.get(rateLimitKey);
    
    if (!rateLimitData || now > rateLimitData.resetTime) {
      // Reset rate limit window
      rateLimitData = { count: 0, resetTime: now + hourInMs };
      rateLimitStore.set(rateLimitKey, rateLimitData);
    }
    
    if (rateLimitData.count >= keyData.rateLimit) {
      return res.status(429).json({ 
        message: 'Rate limit exceeded. Please try again later.',
        limit: keyData.rateLimit,
        resetTime: new Date(rateLimitData.resetTime).toISOString()
      });
    }
    
    // Increment rate limit counter
    rateLimitData.count++;
    rateLimitStore.set(rateLimitKey, rateLimitData);
    
    // Add rate limit headers
    res.setHeader('X-RateLimit-Limit', keyData.rateLimit.toString());
    res.setHeader('X-RateLimit-Remaining', (keyData.rateLimit - rateLimitData.count).toString());
    res.setHeader('X-RateLimit-Reset', new Date(rateLimitData.resetTime).toISOString());
    
    // Record API usage
    await storage.recordApiKeyUsage(keyData.id, req.path, req.method);
    
    // Add user data to request
    req.user = {
      claims: {
        sub: keyData.userId
      },
      apiKey: keyData
    };
    
    next();
  } catch (error) {
    console.error('API authentication error:', error);
    res.status(500).json({ message: 'Authentication error' });
  }
}

export function createPermissionMiddleware(requiredPermissions: string[]) {
  return (req: any, res: Response, next: NextFunction) => {
    // Session users have full access by default
    if (req.user && req.user.claims && req.user.claims.sub && !req.user.apiKey) {
      return next();
    }
    
    const userPermissions = req.user?.apiKey?.permissions || '';
    
    // Check if user has required permissions
    if (requiredPermissions.includes('read-only') && 
        ['read-only', 'upload-only', 'full-access'].includes(userPermissions)) {
      return next();
    }
    
    if (requiredPermissions.includes('upload-only') && 
        ['upload-only', 'full-access'].includes(userPermissions)) {
      return next();
    }
    
    if (requiredPermissions.includes('full-access') && 
        userPermissions === 'full-access') {
      return next();
    }
    
    res.status(403).json({ 
      message: 'Insufficient permissions. Required: ' + requiredPermissions.join(' or '),
      userPermissions 
    });
  };
}

// Combined authentication middleware that supports both session and API key
export function combinedAuth(req: any, res: Response, next: NextFunction) {
  // Check for API key first
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return apiAuthentication(req, res, next);
  }
  
  // Fall back to session authentication
  if (req.user && req.user.claims && req.user.claims.sub) {
    return next();
  }
  
  return res.status(401).json({ 
    message: 'Authentication required. Provide either session cookies or API key.' 
  });
}

// Pre-configured middleware for different permission levels
export const readOnlyApiAccess = [combinedAuth, createPermissionMiddleware(['read-only'])];
export const uploadApiAccess = [combinedAuth, createPermissionMiddleware(['upload-only'])];
export const writeApiAccess = [combinedAuth, createPermissionMiddleware(['full-access'])];
export const fullApiAccess = [combinedAuth, createPermissionMiddleware(['full-access'])];
import { useQuery } from '@tanstack/react-query';

export interface BidLineItem {
  id: string;
  bidId: string;
  costCode: string;
  description: string;
  quantity: number;
  unitPrice: number;
  unit: string;
  total: number;
  category: string;
  createdAt: string;
}

export interface BidInclusionExclusion {
  id: string;
  bidId: string;
  type: 'inclusion' | 'exclusion';
  description: string;
  category: string;
  createdAt: string;
}

export interface StructuredBidData {
  lineItems: BidLineItem[];
  inclusions: BidInclusionExclusion[];
  exclusions: BidInclusionExclusion[];
  totalBidAmount: number;
  lineItemsTotal: number;
  processingMethod: 'structured_form' | 'ai_extracted' | 'manual';
  dataCompleteness: number;
  lastUpdated: string;
}

export function useStructuredBidData(bidId: string) {
  return useQuery<StructuredBidData>({
    queryKey: ['/api/bids', bidId, 'structured-data'],
    enabled: !!bidId,
  });
}

export function calculateDataQuality(data: StructuredBidData): {
  score: number;
  completeness: number;
  hasLineItems: boolean;
  hasScope: boolean;
  hasStructuredData: boolean;
} {
  const hasLineItems = data.lineItems && data.lineItems.length > 0;
  const hasScope = (data.inclusions && data.inclusions.length > 0) || 
                   (data.exclusions && data.exclusions.length > 0);
  const hasStructuredData = hasLineItems || hasScope;
  
  let score = 0;
  if (hasLineItems) score += 50;
  if (hasScope) score += 30;
  if (data.totalBidAmount > 0) score += 20;
  
  const completeness = data.dataCompleteness || 0;
  
  return {
    score: Math.min(score, 100),
    completeness,
    hasLineItems,
    hasScope,
    hasStructuredData
  };
}
import { 
  ChartLine, 
  FileText, 
  Inbox,
  Gavel, 
  Users, 
  BarChart3, 
  TrendingUp,
  Folder, 
  Copy, 
  <PERSON>tings, 
  HelpCircle,
  Plus,
  Heart,
  Target,
  ClipboardList,
  BookOpen,
  Shield
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { useUserRole } from "@/hooks/useUserRole";

// General Contractor sidebar configuration
const gcSidebarItems = [
  { icon: ChartLine, label: "Dashboard", href: "/" },
];

const gcProjectManagement = [
  { icon: FileText, label: "My RFQs", href: "/rfqs" },
  // { icon: ClipboardList, label: "Bid Analysis", href: "/bids" }, // HIDDEN: Commented out per user request
];

const gcContractorNetwork = [
  { icon: Users, label: "Browse Contractors", href: "/contractors" },
  { icon: Heart, label: "My Favorites", href: "/contractors/favorites" },
];

// Contractor sidebar configuration  
const contractorSidebarItems = [
  { icon: ChartLine, label: "Dashboard", href: "/" },
];

const contractorOpportunities = [
  { icon: Target, label: "Available RFQs", href: "/contractors/rfqs" },
];

const contractorBidManagement = [
  { icon: Gavel, label: "My Bids", href: "/bids" },
  // { icon: BarChart3, label: "Performance Analytics", href: "/analytics" }, // HIDDEN: Commented out per user request
];

// Shared sections for both roles
const sharedResources = [
  // { icon: TrendingUp, label: "MatIQ Forecasting", href: "/forecast" }, // HIDDEN: Commented out per user request
  // { icon: Folder, label: "Document Library", href: "/documents" },
  { icon: Copy, label: "Templates", href: "/templates" },
];

const sharedAdministration = [
  { icon: Settings, label: "Settings", href: "/settings" },
  { icon: HelpCircle, label: "Help & Support", href: "/help" },
];

export function Sidebar() {
  const [location, navigate] = useLocation();
  const { user } = useAuth();
  const { role, interfaceMode, isSuperUser, isLoading } = useUserRole();

  // Only allow Analytics access for SuperUser
  const isAnalyticsUser = isSuperUser;
  const isGeneralContractor = interfaceMode === 'general_contractor';
  const isContractor = interfaceMode === 'contractor';

  const handleCreateRFQ = () => {
    navigate("/rfqs");
    const url = new URL(window.location.href);
    url.searchParams.set('create', 'true');
    window.history.pushState({}, '', url);
    window.dispatchEvent(new CustomEvent('openCreateRFQ'));
  };

  // Show loading state while determining role
  if (isLoading) {
    return (
      <aside className="hidden lg:flex flex-col w-64 bg-card border-r border-border">
        <div className="flex-1 overflow-y-auto p-4">
          <div className="flex items-center justify-center h-32">
            <p className="text-muted-foreground text-sm">Loading...</p>
          </div>
        </div>
      </aside>
    );
  }

  const renderSidebarSection = (items: any[], sectionTitle?: string): React.ReactNode => (
    <div className="space-y-1">
      {sectionTitle && (
        <h3 className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
          {sectionTitle}
        </h3>
      )}
      {items.map((item) => (
        <Link key={item.href} href={item.href}>
          <Button
            variant={location === item.href ? "secondary" : "ghost"}
            className={cn(
              "w-full justify-start",
              location === item.href && "bg-primary/10 text-primary"
            )}
            size="sm"
          >
            <item.icon className="mr-3 h-4 w-4" />
            {item.label}
          </Button>
        </Link>
      ))}
    </div>
  );

  return (
    <aside className="hidden lg:flex flex-col w-64 bg-card border-r border-border">
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">
          {/* Quick Create - Only for General Contractors */}
          {isGeneralContractor && (
            <div className="mb-6">
              <Button className="w-full" size="sm" onClick={handleCreateRFQ}>
                <Plus className="mr-2 h-4 w-4" />
                Create RFQ
              </Button>
            </div>
          )}

          {/* Role-specific Main Navigation */}
          {isGeneralContractor && (
            <>
              {renderSidebarSection(gcSidebarItems)}
              <div className="my-4 border-t border-border" />
              {renderSidebarSection(gcProjectManagement, "RFQ and Bid Management")}
              <div className="my-4 border-t border-border" />
              {renderSidebarSection(gcContractorNetwork, "Contractor Network")}
            </>
          )}

          {isContractor && (
            <>
              {renderSidebarSection(contractorSidebarItems)}
              <div className="my-4 border-t border-border" />
              {renderSidebarSection(contractorOpportunities, "Opportunities")}
              <div className="my-4 border-t border-border" />
              {renderSidebarSection(contractorBidManagement, "Bid Management")}
            </>
          )}

          {/* Shared Resources Section */}
          <div className="my-4 border-t border-border" />
          {renderSidebarSection(sharedResources, "Resources")}

          {/* Shared Administration Section */}
          <div className="my-4 border-t border-border" />
          <div className="space-y-1">
            <h3 className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Administration
            </h3>
            {/* Analytics - Role-based access */}
            {isAnalyticsUser && (
              <Link href="/analytics">
                <Button
                  variant={location === "/analytics" ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    location === "/analytics" && "bg-primary/10 text-primary"
                  )}
                  size="sm"
                >
                  <BarChart3 className="mr-3 h-4 w-4" />
                  Analytics Dashboard
                </Button>
              </Link>
            )}
            {/* HIDDEN: Performance Analytics commented out per user request
            {isContractor && !isAnalyticsUser && (
              <Link href="/analytics">
                <Button
                  variant={location === "/analytics" ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    location === "/analytics" && "bg-primary/10 text-primary"
                  )}
                  size="sm"
                >
                  <BarChart3 className="mr-3 h-4 w-4" />
                  Performance Analytics
                </Button>
              </Link>
            )}
            */}
            {sharedAdministration.map((item) => (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={location === item.href ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    location === item.href && "bg-primary/10 text-primary"
                  )}
                  size="sm"
                >
                  <item.icon className="mr-3 h-4 w-4" />
                  {item.label}
                </Button>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </aside>
  );
}
Field-Specific Instructions:

fileName: Use the original name of the PDF file provided.

projectDescription: Find the section that provides a general description or introduction to the project. This may be under a heading like "Project Description," "Introduction," or "Overview."

contactName: Locate the primary point of contact for this project. Look for headings such as "Contact Information," "Point of Contact," or "Procurement Officer."

contactEmail: Find the email address associated with the primary contact person.

projectSummary: Extract the executive summary or abstract of the project. This is often a brief, high-level overview found at the beginning of the document under a "Summary" or "Abstract" heading.

requirements: Identify and extract the list of requirements, scope of work, or technical specifications. If the requirements are listed as bullet points or a numbered list, format them as a JSON array of strings. If they are presented as a single block of text, extract the entire text as a single string.

finalAwardDate: Find the specific date listed for the final award decision. Search for terms like "Final Award Date," "Anticipated Award," or "Decision Date." Please format the date as YYYY-MM-DD.

Example of a good response:

JSON

{
  "fileName": "RFP-2025-Data-Analytics-Platform.pdf",
  "projectDescription": "The purpose of this Request for Proposal (RFP) is to solicit proposals from qualified vendors for the implementation of a new enterprise-level data analytics platform.",
  "contactName": "<PERSON>",
  "contactEmail": "<EMAIL>",
  "projectSummary": "Example Corp is seeking a comprehensive data analytics solution to improve business intelligence and reporting capabilities across all departments. This project aims to consolidate data sources and provide a user-friendly interface for data exploration and visualization.",
  "requirements": [
    "The platform must support integration with our existing SQL databases.",
    "Vendor must provide on-site training for up to 50 employees.",
    "The system must be compliant with GDPR and CCPA regulations.",
    "Provide a detailed data migration plan from our legacy system."
  ],
  "finalAwardDate": "2025-11-15"
}
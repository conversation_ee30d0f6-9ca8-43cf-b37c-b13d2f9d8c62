# Architecture Documentation

## System Overview

Bidaible is a sophisticated AI-powered construction bidding platform built with enterprise-grade TypeScript architecture. The system delivers "wow factor" bid analysis through advanced competitive intelligence, risk assessment, and predictive analytics. Designed for scalability and performance, it features dual authentication, comprehensive API infrastructure, and sophisticated caching strategies.

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   AI Services   │    │   External APIs │
│   (React/TS)    │◄──►│  (Gemini 2.5/   │◄──►│  (Replit Auth/  │
│                 │    │   OpenAI GPT)   │    │   Object Store) │
└─────────┬───────┘    └─────────────────┘    └─────────────────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Server    │◄──►│   Database      │    │   File Storage  │
│   (Express/TS)  │    │ (PostgreSQL +   │    │ (Replit Object  │
│   Dual Auth +   │    │  30+ Indexes)   │    │    Storage)     │
│   Rate Limiting │    └─────────────────┘    └─────────────────┘
└─────────────────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI Analysis   │    │   Cache Layer   │    │   Security &    │
│   Engine        │    │  (In-Memory     │    │   Monitoring    │
│  (Bid Intel +   │    │   TTL/LRU)      │    │  (Audit Logs)   │
│   Risk Assess)  │    └─────────────────┘    └─────────────────┘
└─────────────────┘
```

## Frontend Architecture

### Technology Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite with Hot Module Replacement
- **Routing**: Wouter for lightweight client-side routing
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: TanStack Query for server state
- **Forms**: React Hook Form with Zod validation

### Component Hierarchy
```
App
├── AuthenticatedApp (Protected routes)
│   ├── Layout
│   │   ├── Navbar (Role-based navigation)
│   │   ├── Sidebar (Context-aware menus)
│   │   └── Main Content
│   ├── Pages
│   │   ├── Dashboard (Analytics & metrics with embedded full-width Bid Management)
│   │   ├── RFQs (Upload & management)
│   │   ├── BidManagement (AI analysis dashboard)
│   │   ├── RfqBidManagement (GC view with competitive intelligence)
│   │   ├── Contractors (29 trade categories)
│   │   ├── BidSubmission (Contractor bidding interface)
│   │   ├── Settings (API key management & user preferences)
│   │   └── Analytics (Admin-only insights)
│   └── Components
│       ├── RFQForm (Upload-only AI processing)
│       ├── RFQDetails (with Object Storage PDF viewer)
│       ├── BidAnalyticsDashboard (Competitive intelligence)
│       ├── BidDetailsModal (AI analysis with markdown)
│       ├── ContractorProfile (29 trade categories)
│       ├── APIKeyManagement (Comprehensive JWT token management)
│       └── SettingsPage (Tabbed interface for API keys and preferences)
└── Landing (Public route)
```

### State Management Pattern
```typescript
// Server state with TanStack Query
const { data: rfqs, isLoading } = useQuery({
  queryKey: ['/api/rfqs'],
  queryFn: () => apiRequest('/api/rfqs')
});

// Local UI state with React hooks
const [isModalOpen, setIsModalOpen] = useState(false);

// Form state with React Hook Form
const form = useForm<FormData>({
  resolver: zodResolver(schema)
});
```

### Routing Structure
```
/                    # Landing page (unauthenticated)
/dashboard           # Main dashboard with embedded full-width Bid Management Dashboard for immediate AI analysis access
/rfqs               # RFQ management & creation
/rfqs/:id/bids      # Bid management dashboard with AI analysis
/contractors        # Contractor profiles (29 trade types)
/contractors/rfqs   # Contractor RFQ browsing interface
/bids               # Contractor bid management
/analytics          # Admin-only analytics dashboard
/settings           # Comprehensive settings with API key management, preferences, and account settings
/help               # Interactive API documentation
/materials          # Material forecasting
```

## Backend Architecture

### Technology Stack
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Authentication**: Dual system (Replit Auth + JWT API keys)
- **Database**: PostgreSQL with Drizzle ORM
- **AI Processing**: Google Gemini 2.5 Pro with OpenAI GPT-4.1-mini fallback
- **File Storage**: Replit Object Storage
- **Caching**: In-memory caching with TTL and LRU eviction

### Core Services Architecture

#### AI Analysis Engine
```typescript
// Competitive Intelligence System
interface BidAnalysisResult {
  aiSummary: string;
  competitiveScore: number;
  aiAnalysis: {
    strengths: string[];
    concerns: string[];
    competitivePosition: string;
    priceAnalysis: string;
    timelineAssessment: string;
    contractorProfile: string;
    riskFactors: string[];
    recommendations: string[];
  };
}

// Multi-factor scoring algorithm
const competitiveScore = calculateCompetitiveScore({
  priceCompetitiveness: 0.4,    // 40% weight
  extractionConfidence: 0.2,    // 20% weight  
  submissionTiming: 0.2,        // 20% weight
  completeness: 0.2             // 20% weight
});
```

#### API Authentication System
```typescript
// Dual authentication middleware
interface AuthenticationFlow {
  session: {
    provider: 'Replit Auth';
    storage: 'PostgreSQL-backed sessions';
    flow: 'OAuth 2.0 with automatic management';
  };
  apiKey: {
    technology: 'JWT with SHA-256 hashing';
    permissions: 'read-only' | 'upload-only' | 'full-access';
    security: 'Rate limiting + usage tracking + audit trails';
  };
}
```

#### Database Schema with Strategic Indexing
```sql
-- 30+ strategic indexes for optimal performance
CREATE INDEX CONCURRENTLY idx_bids_rfq_status ON bids (rfq_id, status);
CREATE INDEX CONCURRENTLY idx_bids_contractor_submitted ON bids (contractor_id, submitted_at);
CREATE INDEX CONCURRENTLY idx_rfqs_created_status ON rfqs (created_by, status, created_at);
CREATE INDEX CONCURRENTLY idx_api_keys_user_active ON api_keys (user_id, is_active, expires_at);
CREATE INDEX CONCURRENTLY idx_api_usage_key_date ON api_key_usage (api_key_id, date);
```

### Service Layer Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   aiService     │    │bidAnalysisService│   │  cacheService   │
│                 │    │                 │    │                 │
│ • Document AI   │◄──►│ • Competitive   │◄──►│ • TTL Cache     │
│ • Text Extract  │    │   Intelligence  │    │ • LRU Eviction  │
│ • Multi-model   │    │ • Risk Analysis │    │ • Pattern       │
│   Fallback      │    │ • Score Calc    │    │   Invalidation  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Middleware Stack
```typescript
// Security & Performance Middleware
app.use(helmet());                    // Security headers
app.use(compression());               // Response compression
app.use(rateLimiter);                 // Rate limiting
app.use(auditLogger);                 // Audit trail
app.use(authenticationMiddleware);    // Dual auth
app.use(permissionMiddleware);        // Access control
app.use(cacheMiddleware);            // Response caching
```

## AI Processing Pipeline

### Document Analysis Flow
```
Document Upload → PDF.js Extraction → Text Cleaning → AI Analysis → Data Mapping → Database Storage
      ↓                ↓                   ↓             ↓             ↓              ↓
  Validation      24K+ characters    Remove nulls   Multi-model   Field mapping   Auto-save
  50MB limit      support           & control      processing    & validation    complete
  PDF/TXT/CSV     reliability       characters     reliability   error handling  record
```

### Bid Intelligence Pipeline
```
Bid Submission → AI Content Analysis → Competitive Scoring → Risk Assessment → Market Intelligence
      ↓                    ↓                     ↓                  ↓                 ↓
  File upload        Extract scope,         Multi-factor       Timeline &         Positioning
  & validation       amount, timeline      evaluation         contractor         & negotiation
  Multi-format       & conditions          algorithm          reliability        insights
```

### AI Model Strategy
```typescript
// Primary/Fallback Architecture
const aiStrategy = {
  primary: 'Google Gemini 2.5 Pro',
  fallback: 'OpenAI GPT-4.1-mini',
  specialization: {
    documents: 'Gemini for comprehensive analysis',
    competitive: 'Multi-model for reliability',
    extraction: 'PDF.js + AI for accuracy'
  }
};
```

## Performance & Security Architecture

### Caching Strategy
```typescript
// Multi-layer caching system
interface CacheLayer {
  memory: {
    strategy: 'TTL + LRU eviction';
    ttl: '5-60 minutes depending on data type';
    invalidation: 'Pattern-based cache busting';
  };
  database: {
    indexes: '30+ strategic indexes for query optimization';
    pooling: 'Connection pooling for scalability';
  };
  api: {
    rateLimit: 'Per-key configurable limits';
    analytics: 'Real-time usage tracking';
  };
}
```

### Security Architecture
```typescript
// Multi-layer security system
interface SecurityLayer {
  authentication: {
    session: 'PostgreSQL-backed with secure cookies';
    apiKey: 'JWT with SHA-256 hashing';
    oauth: 'Replit Auth with OpenID Connect';
  };
  authorization: {
    rbac: 'Role-based access control';
    scoped: 'API key permission scoping';
    audit: 'Comprehensive audit logging';
  };
  dataProtection: {
    encryption: 'In-transit and at-rest encryption';
    validation: 'Input sanitization and Zod validation';
    headers: 'Security headers via Helmet.js';
  };
}
```

## Backend Architecture

### Technology Stack
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Replit Auth (OpenID Connect)
- **File Processing**: Multer for uploads
- **Session Management**: PostgreSQL-backed sessions

### Service Layer Pattern
```
Controller Layer (routes.ts)
    ↓
Business Logic Layer (services/)
    ↓
Data Access Layer (storage.ts)
    ↓
Database Layer (PostgreSQL)
```

### API Route Structure
```
/api/auth/*          # Authentication endpoints
/api/rfqs/*          # RFQ management
/api/contractors/*   # Contractor management
/api/bids/*          # Bid management
/api/forecast-materials/* # Material forecasting
/api/dashboard/*     # Dashboard statistics
```

### Service Architecture
```typescript
// Service interface pattern
interface IStorage {
  getUser(id: string): Promise<User | undefined>;
  createRfq(rfq: InsertRfq): Promise<Rfq>;
  // ... other methods
}

// Implementation
class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }
}
```

## Database Architecture

### Schema Design
The database follows a normalized relational design with clear entity relationships:

```sql
-- Core entities
users              # User accounts and profiles
contractors        # Contractor information
rfqs              # Request for Quote records
bids              # Bid submissions
rfq_documents     # File attachments
forecast_materials # Material pricing data

-- Session management
sessions          # User session storage
```

### Entity Relationships
```
Users (1:1) ←→ Contractors
Users (1:N) ←→ RFQs
RFQs (1:N) ←→ RFQ_Documents
RFQs (1:N) ←→ Bids
Contractors (1:N) ←→ Bids
```

### Database Schema
```typescript
// User management
export const users = pgTable("users", {
  id: varchar("id").primaryKey(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});

// RFQ management with AI extracted data
export const rfqs = pgTable("rfqs", {
  id: varchar("id").primaryKey(),
  projectName: varchar("project_name").notNull(),
  projectLocation: varchar("project_location"),
  description: text("description"),
  tradeCategory: tradeEnum("trade_category").notNull(),
  // AI-extracted fields from documents
  contactName: varchar("contact_name"),
  contactEmail: varchar("contact_email"),
  projectSummary: text("project_summary"),
  requirements: text("requirements"),
  finalAwardDate: varchar("final_award_date"),
  status: rfqStatusEnum("status").notNull(),
  dueDate: timestamp("due_date"),
  createdBy: varchar("created_by").notNull(),
  extractedData: jsonb("extracted_data"), // AI-extracted fields
  createdAt: timestamp("created_at").defaultNow()
});
```

### Data Access Patterns
```typescript
// Repository pattern with Drizzle ORM
class DatabaseStorage {
  async getRfqs(): Promise<Rfq[]> {
    return await db.select().from(rfqs)
      .leftJoin(users, eq(rfqs.createdBy, users.id))
      .orderBy(desc(rfqs.createdAt));
  }

  async createRfq(rfq: InsertRfq): Promise<Rfq> {
    const [newRfq] = await db.insert(rfqs).values(rfq).returning();
    return newRfq;
  }
}
```

## AI Processing Architecture

### AI Service Layer
```typescript
// Configurable AI model system
const PRIMARY_MODEL = process.env.PRIMARY_MODEL || "openai";
const OPENAI_MODEL = "gpt-4o-mini-2025-04-14";
const GEMINI_MODEL = "gemini-2.5-pro";
const GROQ_MODEL = "llama-3.3-70b-versatile";

// Processing pipeline with multi-provider support
Document Upload → Text Extraction → AI Analysis (Multi-Provider) → Data Mapping → Database Storage
```

### Document Processing Flow
```
1. File Upload (Multer)
   ↓
2. File Validation (Type, Size)
   ↓
3. Text Extraction (AI Model)
   ↓
4. Data Structuring (JSON Schema)
   ↓
5. Database Mapping (Type Conversion)
   ↓
6. Storage (PostgreSQL + File System)
```

### AI Model Integration
```typescript
// Abstracted AI service interface
interface AIService {
  extractText(fileBuffer: Buffer, mimeType: string): Promise<string>;
  extractStructuredData(text: string): Promise<ExtractedData>;
}

// Implementation for multiple providers
class OpenAIService implements AIService {
  async extractStructuredData(text: string): Promise<ExtractedData> {
    const completion = await openai.chat.completions.create({
      model: OPENAI_MODEL,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: text }
      ],
      response_format: { type: "json_object" }
    });
    return JSON.parse(completion.choices[0].message.content);
  }
}

class GroqService implements AIService {
  async extractStructuredData(text: string): Promise<ExtractedData> {
    const completion = await groq.chat.completions.create({
      model: GROQ_MODEL,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: text }
      ],
      response_format: { type: "json_object" }
    });
    return JSON.parse(completion.choices[0].message.content);
  }
}
```

## Authentication & Authorization

### Authentication Flow
```
1. User clicks login → /api/login
2. Redirect to Replit OAuth
3. User authenticates with Replit
4. Callback to /api/callback
5. Create/update user session
6. Redirect to application
```

### Session Management
```typescript
// PostgreSQL-backed sessions
app.use(session({
  store: new pgStore({
    conString: process.env.DATABASE_URL,
    tableName: 'sessions'
  }),
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false
}));
```

### Authorization Middleware
```typescript
// Role-based access control
export const isAuthenticated: RequestHandler = async (req, res, next) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "Unauthorized" });
  }
  // Token refresh logic
  next();
};

// Role checking
export const requireRole = (role: UserRole) => (req, res, next) => {
  if (req.user.role !== role) {
    return res.status(403).json({ message: "Forbidden" });
  }
  next();
};
```

## File Management Architecture

### Upload Pipeline
```
Client Upload → Multer Middleware → File Validation → AI Processing → Database Reference → File Storage
```

### File Storage Strategy
```typescript
// Local development storage
const storage = multer.diskStorage({
  destination: './uploads',
  filename: (req, file, cb) => {
    cb(null, generateUniqueId() + path.extname(file.originalname));
  }
});

// Production cloud storage (configurable)
const cloudStorage = multer.memoryStorage(); // For S3/GCS upload
```

### File Processing Architecture
```typescript
// Multi-format support
const processors = {
  '.pdf': processPdfDocument,
  '.docx': processWordDocument,
  '.txt': processTextDocument
};

async function processDocument(filePath: string, originalName: string) {
  const extension = path.extname(originalName);
  const processor = processors[extension];
  
  if (!processor) {
    throw new Error(`Unsupported file type: ${extension}`);
  }
  
  return await processor(filePath);
}
```

## Security Architecture

### Data Protection
- **Input Validation**: Zod schemas for all API inputs
- **SQL Injection Prevention**: Parameterized queries with Drizzle ORM
- **XSS Protection**: Content sanitization and CSP headers
- **CSRF Protection**: Session-based token validation

### Authentication Security
```typescript
// Secure session configuration
app.use(session({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 1 week
  },
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false
}));
```

### File Upload Security
```typescript
// File type validation
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.csv', '.txt'];
  const ext = path.extname(file.originalname);
  
  if (allowedTypes.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type'), false);
  }
};

// Size limits
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB
});
```

## Performance Architecture

### Frontend Optimization
```typescript
// Code splitting with React.lazy
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const RFQs = React.lazy(() => import('./pages/RFQs'));

// Memoization for expensive components
const MemoizedDataTable = React.memo(DataTable);

// Query optimization with TanStack Query
const { data } = useQuery({
  queryKey: ['/api/rfqs'],
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000 // 10 minutes
});
```

### Backend Optimization
```typescript
// Connection pooling
export const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000
});

// Query optimization
async function getRfqsWithStats() {
  return await db
    .select({
      rfq: rfqs,
      bidCount: count(bids.id)
    })
    .from(rfqs)
    .leftJoin(bids, eq(rfqs.id, bids.rfqId))
    .groupBy(rfqs.id)
    .orderBy(desc(rfqs.createdAt));
}
```

### Caching Strategy
```typescript
// Memory caching for frequent queries
const cache = new Map();

async function getCachedData(key: string, fetcher: () => Promise<any>) {
  if (cache.has(key)) {
    return cache.get(key);
  }
  
  const data = await fetcher();
  cache.set(key, data);
  setTimeout(() => cache.delete(key), 5 * 60 * 1000); // 5 min TTL
  
  return data;
}
```

## Error Handling Architecture

### Frontend Error Boundaries
```typescript
class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Application error:', error, errorInfo);
    // Send to error tracking service
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }
    return this.props.children;
  }
}
```

### Backend Error Handling
```typescript
// Global error handler
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
  const status = err.status || 500;
  const message = err.message || "Internal Server Error";
  
  // Log error
  console.error({
    error: err,
    request: {
      method: req.method,
      url: req.url,
      userId: req.user?.id
    }
  });
  
  res.status(status).json({ message });
});

// Async error wrapper
const asyncHandler = (fn: Function) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};
```

## Deployment Architecture

### Development Environment
```
Vite Dev Server (Frontend) + Express Server (Backend) → Single Port (5000)
```

### Production Environment
```
Load Balancer → Application Servers → Database Cluster
                ↓
              File Storage (S3/GCS)
                ↓
              CDN (Cloudflare)
```

### Container Architecture
```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
RUN npm run build
EXPOSE 5000
CMD ["npm", "start"]
```

## Monitoring & Observability

### Application Metrics
```typescript
// Performance monitoring
const performanceMonitor = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log({
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration,
      timestamp: new Date().toISOString()
    });
  });
  
  next();
};
```

### Health Checks
```typescript
// Health endpoint
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
    uptime: process.uptime(),
    checks: {
      database: await checkDatabase(),
      ai_services: await checkAIServices()
    }
  };
  
  res.json(health);
});
```

## Scalability Considerations

### Horizontal Scaling
- Stateless application design
- Session storage in PostgreSQL
- File storage on cloud providers
- Load balancer configuration

### Vertical Scaling
- Database connection pooling
- Query optimization
- Memory management
- CPU-intensive task optimization

### Future Architecture Enhancements
- Microservices architecture
- Event-driven communication
- Background job processing
- Real-time WebSocket connections
- Advanced caching layers (Redis)
- Message queues for async processing